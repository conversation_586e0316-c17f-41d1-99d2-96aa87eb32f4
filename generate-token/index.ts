import dotenv from "dotenv";
import fs from "fs/promises";
import path from "path";
import process from "process";
import { <PERSON><PERSON><PERSON> } from "buffer";

const isAllowedEnv =
	process.env.VERCEL_ENV === "preview" ||
	process.env.VERCEL_ENV === undefined ||
  process.env.NODE_ENV === "development";

if (!isAllowedEnv) {
	process.exit(0);
}

const envPath = path.resolve(
	process.cwd(),
	process.env.NODE_ENV === "development" ?
		".env.local"
		: process.env.VERCEL_ENV === "preview"
			? ".env.preview"
			: ".env.local"
);

dotenv.config({ path: envPath });

let token: string | null = null;
let tokenExpiresAt = 0;

const CACHE_PATH = path.join(process.cwd(), "public/wp-token.json");
const EXPIRATION_BUFFER_MS = 30_000;

const initToken = async () => {
	const now = Date.now();

	if (token && now < tokenExpiresAt) {
		return token;
	}

	try {
		const file = await fs.readFile(CACHE_PATH, "utf-8");
		const parsed = JSON.parse(file);

		if (parsed.token && parsed.expiresAt > now) {
			token = parsed.token;
			tokenExpiresAt = parsed.expiresAt;
			return token;
		} else {
			throw new Error("Token expired or invalid");
		}
	} catch {
		console.warn("Token cache not found or invalid. Creating placeholder.");
		
		if (process.env.NODE_ENV !== "production") {
			await fs.writeFile(CACHE_PATH, JSON.stringify({ token: "", expiresAt: 0 }, null, 2));
		}
		
		console.log("Fetching new token");
		return fetchToken();
	}
};

async function fetchToken() {
	const username = process.env.WP_USERNAME;
	const password = process.env.WP_PASSWORD;
	const baseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL?.replace("/graphql", "");

	if (!username || !password || !baseUrl) {
		throw new Error("Missing environment variables");
	}
	
	const res = await fetch(`${baseUrl}/wp-json/jwt-auth/v1/token`, {
		method: "POST",
		headers: { "Content-Type": "application/x-www-form-urlencoded" },
		body: new URLSearchParams({
			username,
			password,
		}),
	});

	if (!res.ok) {
		const errorText = await res.text();
		throw new Error(`Token fetch failed: ${res.status} - ${errorText}`);
	}

	const data = await res.json();
	token = data.token;

	if (!token) {
		throw new Error("Token not found");
	}

	const [, payload] = token.split(".");
	const { exp } = JSON.parse(Buffer.from(payload, "base64").toString());
	tokenExpiresAt = exp * 1000 - EXPIRATION_BUFFER_MS;

	await fs.mkdir(path.dirname(CACHE_PATH), { recursive: true });
	await fs.writeFile(
		CACHE_PATH,
		JSON.stringify({ token, expiresAt: tokenExpiresAt }, null, 2),
		"utf-8"
	);

	return token;
}

initToken();