const { exec } = require("child_process");
const express = require("express");
const fetch = require("node-fetch");
const fs = require("fs/promises");
const path = require("path");
const glob = require("glob");
const yaml = require("yaml");
require("dotenv").config();

const PORT = process.env.PORT || 4000;
const TARGET_URL = "https://api.onesource.io/v1/ethereum/graphql";
const API_TOKEN = process.env.ONESOURCE_API_TOKEN || "";

const app = express();
app.use(express.json());

// Load all YAML docs from local files (queries and types)
async function loadDocs() {
  const files = glob.sync("schema-overrides/**/*.yaml");
  const docs = {};

  for (const file of files) {
    const content = await fs.readFile(file, "utf8");
    const parsed = yaml.parse(content); // Parse YAML to JS object
    const fileName = path.basename(file, ".yaml");
    const parentDir = path.basename(path.dirname(file));

    if (parentDir === "queries") {
      // This is a query file, e.g. balance.yaml
      docs.Query = docs.Query || { fields: {} };
      docs.Query.fields[fileName] = parsed.Query?.fields?.[fileName] || parsed.fields?.[fileName] || {};

      Object.keys(parsed).forEach((key) => {
        if (key !== "Query") {
          docs[key] = parsed[key];
        }
      });
    } else {
      // This is a type file (objects, inputs, enums, etc.), e.g. Balance.yaml
      if (parsed[fileName]) {
        docs[fileName] = parsed[fileName];
      } else {
        docs[fileName] = parsed;
      }
    }
  }
  return docs;
}

// Merge local YAML docs into introspected schema
function mergeDocs(schema, docs) {
  if (!schema?.types) return;

  schema.types.forEach((type) => {
    const docType = docs[type.name];
    if (!docType) return;

    // Update type-level description
    if (docType.description) {
      type.description = docType.description;
    }

    // Update field descriptions and argument descriptions
    if (type.fields && docType.fields) {
      type.fields.forEach((field) => {
        const docField = docType.fields[field.name];
        if (!docField) return;

        if (docField.description) {
          field.description = docField.description;
        }

        // Merge arguments
        if (field.args && docField.args) {
          field.args.forEach((arg) => {
            const docArg = docField.args[arg.name];
            if (typeof docArg === "string") {
              arg.description = docArg;
            }
          });
        }
      });
    }

    // Update enum value descriptions
    if (type.enumValues && docType.values) {
      type.enumValues.forEach((enumVal) => {
        const docEnum = docType.values[enumVal.name];
        if (docEnum?.description) {
          enumVal.description = docEnum.description;
        }
      });
    }

    // Update input field descriptions
    if (type.inputFields && docType.inputFields) {
      type.inputFields.forEach((input) => {
        const docInput = docType.inputFields[input.name];
        if (docInput?.description) {
          input.description = docInput.description;
        }
      });
    }
  });
}

// Check if the request is a GraphQL introspection query
function isIntrospection(body) {
  return (
    body?.operationName === "IntrospectionQuery" ||
    body?.query?.includes("__schema") ||
    body?.query?.includes("__type")
  );
}

// Health check endpoint (for testing the server)
app.get("/", (_req, res) => {
  res.send("GraphQL middleware running");
});

// Proxy incoming /graphql requests to the real GraphQL API
// If it is an introspection request, patch the result with local docs
app.post("/graphql", async (req, res) => {
  const introspection = isIntrospection(req.body);

  try {
    const upstream = await fetch(TARGET_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-bp-token": API_TOKEN,
      },
      body: JSON.stringify(req.body),
    });

    const data = await upstream.json();

    // If it's introspection result — patch schema with local YAML docs 
    if (introspection && data?.data?.__schema) {
      const docs = await loadDocs();
      mergeDocs(data.data.__schema, docs);
    }

    res.status(upstream.status).json(data);
  } catch (e) {
    console.error("Middleware error:", e);
    res.status(500).json({ error: e.message });
  }
});

app.get("/generate", (req, res) => {
  const start = Date.now();
  exec("node scripts/generateCustomDocs.js", (error, stdout, stderr) => {
    const duration = Date.now() - start;
    if (error) {
      console.error("Generation error:", stderr);
      return res.status(500).json({ error: stderr, duration });
    }
    console.log(`Generation complete in ${duration}ms`);
    res.json({ message: "Generation complete", duration });
  });
});

app.listen(PORT, () => {
  console.log(`Middleware listening at http://localhost:${PORT}/graphql`);
}).on('error', (err) => {
  console.error("Failed to start server:", err);
});