export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  try {
    let rawBody = "";
    for await (const chunk of req) {
      rawBody += chunk;
    }

    // Validate it's valid JSON
    JSON.parse(rawBody); // Will throw if invalid

    const response = await fetch(
      "https://api.onesource.io/v1/ethereum/graphql",
      {
        method: req.method,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-bp-token": "BP-01JPMT831KBC098A63KXKK5V44",
        },
        body: rawBody,
      }
    );

    const text = await response.text(); // GraphiQL expects raw text
    res.status(response.status).send(text);
  } catch (err) {
    console.error("Proxy error:", err);
    res
      .status(500)
      .json({ error: "Internal Server Error", details: err.message });
  }
}
