module.exports = {
  docs: [
    {
      type: 'doc',
      id: 'intro',
    },
    {
      type: 'category',
      label: 'Getting Started',
      items: [
        'getting-started/subscription-plans',
        'getting-started/get-an-api-key',
        'getting-started/authentication',
        {
          type: 'category',
          label: 'Playgrounds',
          items: [
            'getting-started/playgrounds/graphiql',
            'getting-started/playgrounds/onesource',
            'getting-started/playgrounds/apollo',
          ],
          link: {
            type: 'doc',
            id: 'getting-started/playgrounds/README'
          },
        },
      ],
      link: {
        type: 'doc',
        id: 'getting-started/README'
      },
      collapsed: true
    },
    {
      type: 'doc',
      id: 'account-access'
    },
    {
      type: 'category',
      label: 'Guides',
      items: [
        'guides/integrating-onesource-queries',
        'guides/working-with-api-responses',
      ],
      link: {
        type: 'doc',
        id: 'guides/README'
      },
      collapsed: true
    },
    {
      type: 'category',
      label: 'OneSource Web3 API Reference',
      link: {
        type: 'doc',
        id: 'onesource-web3-api-reference/README'
      },
      collapsed: false,
      items: [
        {
          type: 'category',
          label: 'Queries',
          items: [
            'onesource-web3-api-reference/operations/queries/balance',
            'onesource-web3-api-reference/operations/queries/balances',
            'onesource-web3-api-reference/operations/queries/contract',
            'onesource-web3-api-reference/operations/queries/contracts',
            'onesource-web3-api-reference/operations/queries/token',
            'onesource-web3-api-reference/operations/queries/tokens',
          ],
          link: {
            type: 'doc',
            id: 'onesource-web3-api-reference/queries/README'
          },
          collapsed: false
        },
        {
          type: 'category',
          label: 'Types',
          link: {
            type: 'doc',
            id: 'onesource-web3-api-reference/types/README'
          },
          items: [
            {
              type: 'category',
              label: 'Enums',
              items: [
                'onesource-web3-api-reference/types/enums/balance-order-by',
                'onesource-web3-api-reference/types/enums/contract-order-by',
                'onesource-web3-api-reference/types/enums/contract-type',
                'onesource-web3-api-reference/types/enums/image-order-by',
                'onesource-web3-api-reference/types/enums/image-status',
                'onesource-web3-api-reference/types/enums/metadata-status',
                'onesource-web3-api-reference/types/enums/order-direction',
                'onesource-web3-api-reference/types/enums/ordering',
                'onesource-web3-api-reference/types/enums/thumbnail-order-by',
                'onesource-web3-api-reference/types/enums/thumbnail-preset',
                'onesource-web3-api-reference/types/enums/thumbnail-status',
                'onesource-web3-api-reference/types/enums/token-order-by',
                'onesource-web3-api-reference/types/enums/token-uristatus'
              ],
              collapsed: true
            },
            {
              type: 'category',
              label: 'Inputs',
              items: [
                'onesource-web3-api-reference/types/inputs/balance-filter',
                'onesource-web3-api-reference/types/inputs/contract-filter',
                'onesource-web3-api-reference/types/inputs/image-filter',
                'onesource-web3-api-reference/types/inputs/thumbnail-filter',
                'onesource-web3-api-reference/types/inputs/token-filter'
              ],
              collapsed: true
            },
            {
              type: 'category',
              label: 'Interfaces',
              items: [
                'onesource-web3-api-reference/types/interfaces/plural'
              ],
              collapsed: true
            },
            {
              type: 'category',
              label: 'Objects',
              items: [
                'onesource-web3-api-reference/types/objects/balance',
                'onesource-web3-api-reference/types/objects/balances',
                'onesource-web3-api-reference/types/objects/contract',
                'onesource-web3-api-reference/types/objects/contracts',
                'onesource-web3-api-reference/types/objects/image',
                'onesource-web3-api-reference/types/objects/thumbnail',
                'onesource-web3-api-reference/types/objects/token',
                'onesource-web3-api-reference/types/objects/tokens',
              ],
              collapsed: true
            },
            {
              type: 'category',
              label: 'Scalars',
              items: [
                'onesource-web3-api-reference/types/scalars/id',
                'onesource-web3-api-reference/types/scalars/int',
                'onesource-web3-api-reference/types/scalars/string',
                'onesource-web3-api-reference/types/scalars/time',
                'onesource-web3-api-reference/types/scalars/uint-64',
              ],
              collapsed: true
            }
          ],
          collapsed: false
        }
      ]
    }
  ]
};
