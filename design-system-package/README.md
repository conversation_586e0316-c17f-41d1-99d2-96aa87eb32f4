# @blockparty/design-system

A shared design system for Blockparty projects, providing consistent UI components and design tokens across Docusaurus and Next.js applications.

## Installation

```bash
npm install @blockparty/design-system
```

## Quick Start

### For Next.js with Chakra UI

```typescript
import { <PERSON><PERSON>Provider } from '@chakra-ui/react';
import { createChakraTheme } from '@blockparty/design-system/adapters/chakra';
import { Button, Card } from '@blockparty/design-system';

const theme = createChakraTheme();

function App() {
  return (
    <ChakraProvider theme={theme}>
      <Card title="Welcome" description="Get started with our design system">
        <Button variant="primary">Get Started</Button>
      </Card>
    </ChakraProvider>
  );
}
```

### For Docusaurus

```javascript
// docusaurus.config.js
module.exports = {
  stylesheets: [
    '@blockparty/design-system/dist/docusaurus.css'
  ],
};
```

```mdx
import { But<PERSON>, <PERSON> } from '@blockparty/design-system';

# Welcome

<Card title="API Documentation" description="Learn how to use our GraphQL API">
  <Button variant="primary">View Docs</Button>
</Card>
```

## Components

### Button
```typescript
<Button variant="primary" size="lg">Primary Button</Button>
<Button variant="secondary" size="md">Secondary Button</Button>
<Button variant="ghost" size="sm">Ghost Button</Button>
<Button variant="icon" size="md"><SearchIcon /></Button>
```

### Input
```typescript
<Input 
  variant="default" 
  label="Email" 
  placeholder="Enter your email"
  required 
/>
<Input 
  variant="contact" 
  label="Message" 
  multiline 
  rows={4} 
/>
```

### Typography
```typescript
<Typography variant="h1">Main Heading</Typography>
<Typography variant="body1">Body text content</Typography>
<Typography variant="caption" color="gray.500">Caption text</Typography>
```

### Card
```typescript
<Card 
  variant="feature"
  title="Feature Title"
  description="Feature description"
  image="/feature-image.jpg"
  tags={["GraphQL", "API"]}
/>
```

## Design Tokens

### Colors
```typescript
import { colors } from '@blockparty/design-system/tokens';

// Brand colors
colors.primary        // #19DFAE
colors.primaryDark    // #071F21
colors.darkBrown      // #311D19

// Semantic colors
colors.success        // #19DFAE
colors.warning        // #EF992F
colors.error          // #C63131
```

### Typography
```typescript
import { typography } from '@blockparty/design-system/tokens';

typography.fonts.body     // Figtree, system-ui, sans-serif
typography.fonts.heading  // Mabry Medium Pro, Figtree, sans-serif
typography.fonts.mono     // MonaspaceNeon, monospace

typography.fontSizes.sm   // 0.875rem
typography.fontSizes.md   // 1rem
typography.fontSizes.lg   // 1.125rem
```

### Spacing
```typescript
import { spacing } from '@blockparty/design-system/tokens';

spacing[2]    // 0.5rem (8px)
spacing[4]    // 1rem (16px)
spacing[6]    // 1.5rem (24px)
```

## Adapters

### Chakra UI Adapter
```typescript
import { createChakraTheme } from '@blockparty/design-system/adapters/chakra';

const theme = createChakraTheme();
// Use with ChakraProvider
```

### Docusaurus Adapter
```typescript
import { applyDocusaurusTheme } from '@blockparty/design-system/adapters/docusaurus';

// Apply theme programmatically
applyDocusaurusTheme();
```

## Development

```bash
# Install dependencies
npm install

# Build the package
npm run build

# Watch for changes
npm run dev

# Run tests
npm test

# Run Storybook
npm run storybook
```

## License

UNLICENSED - Private package for Blockparty projects only.
