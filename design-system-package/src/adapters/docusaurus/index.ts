import { generateCSSString, colors, typography, spacing } from '../../tokens';

// Generate CSS custom properties for Docusaurus
export const generateDocusaurusCSS = () => {
  const baseCSS = generateCSSString();
  
  // Additional Docusaurus-specific CSS variables
  const docusaurusVars = `
/* Docusaurus Integration */
:root {
  /* Override Infima variables with our design tokens */
  --ifm-color-primary: ${colors.primary};
  --ifm-color-primary-dark: ${colors.primaryDark};
  --ifm-color-primary-darker: ${colors.midDataGreen};
  --ifm-color-primary-darkest: ${colors.darkGreen};
  --ifm-color-primary-light: ${colors.primaryLight};
  --ifm-color-primary-lighter: ${colors.dataGreenLight};
  --ifm-color-primary-lightest: ${colors.alpha.primary10};
  
  /* Background colors */
  --ifm-background-color: ${colors.white};
  --ifm-background-surface-color: ${colors.desertSand};
  
  /* Text colors */
  --ifm-font-color-base: ${colors.darkBrown};
  --ifm-font-color-secondary: ${colors.alpha.dark60};
  
  /* Typography */
  --ifm-font-family-base: ${typography.fonts.body};
  --ifm-heading-font-family: ${typography.fonts.heading};
  --ifm-font-family-monospace: ${typography.fonts.mono};
  
  /* Font sizes */
  --ifm-font-size-base: ${typography.fontSizes.md};
  --ifm-h1-font-size: ${typography.fontSizes['5xl']};
  --ifm-h2-font-size: ${typography.fontSizes['4xl']};
  --ifm-h3-font-size: ${typography.fontSizes['3xl']};
  --ifm-h4-font-size: ${typography.fontSizes['2xl']};
  --ifm-h5-font-size: ${typography.fontSizes.xl};
  --ifm-h6-font-size: ${typography.fontSizes.lg};
  
  /* Spacing */
  --ifm-spacing-horizontal: ${spacing[4]};
  --ifm-spacing-vertical: ${spacing[4]};
  --ifm-global-spacing: ${spacing[4]};
  
  /* Border radius */
  --ifm-border-radius: 0.5rem;
  --ifm-button-border-radius: 9999px;
  
  /* Navbar */
  --ifm-navbar-background-color: ${colors.white};
  --ifm-navbar-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --ifm-navbar-item-padding-horizontal: ${spacing[4]};
  --ifm-navbar-item-padding-vertical: ${spacing[2]};
  
  /* Footer */
  --ifm-footer-background-color: ${colors.primaryDark};
  --ifm-footer-color: ${colors.desertSand};
  --ifm-footer-link-color: ${colors.primary};
  --ifm-footer-title-color: ${colors.white};
  
  /* Code blocks */
  --ifm-code-background: ${colors.alpha.dark25};
  --ifm-code-color: ${colors.darkBrown};
  --ifm-pre-background: ${colors.primaryDark};
  --ifm-pre-color: ${colors.desertSand};
  
  /* Alerts */
  --ifm-alert-border-radius: 0.5rem;
  --ifm-alert-border-width: 0;
  --ifm-alert-border-left-width: 4px;
  
  /* Tables */
  --ifm-table-border-color: ${colors.toastedOat};
  --ifm-table-head-background: ${colors.alpha.primary10};
  --ifm-table-stripe-background: ${colors.alpha.desertSand25};
  
  /* Pagination */
  --ifm-pagination-nav-border-radius: 0.5rem;
  --ifm-pagination-nav-color-hover: ${colors.primary};
  
  /* TOC */
  --ifm-toc-border-color: ${colors.toastedOat};
  --ifm-toc-link-color: ${colors.alpha.dark60};
}

[data-theme='dark'] {
  /* Dark theme overrides */
  --ifm-background-color: ${colors.primaryDark};
  --ifm-background-surface-color: ${colors.midnightGreen};
  --ifm-font-color-base: ${colors.desertSand};
  --ifm-font-color-secondary: ${colors.alpha.desertSand80};
  
  /* Navbar dark */
  --ifm-navbar-background-color: ${colors.darkGreen};
  --ifm-navbar-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
  
  /* Code blocks dark */
  --ifm-code-background: ${colors.alpha.desertSand25};
  --ifm-code-color: ${colors.desertSand};
  
  /* Tables dark */
  --ifm-table-border-color: ${colors.alpha.desertSand25};
  --ifm-table-head-background: ${colors.alpha.primary25};
  --ifm-table-stripe-background: ${colors.alpha.dark25};
  
  /* TOC dark */
  --ifm-toc-border-color: ${colors.alpha.desertSand25};
  --ifm-toc-link-color: ${colors.alpha.desertSand80};
}

/* Custom component styles for Docusaurus */
.bp-button {
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: var(--ifm-font-family-base);
  font-weight: 500;
  line-height: 1.25;
  text-decoration: none;
  transition: all 0.15s ease;
  outline: none;
}

.bp-button--primary {
  background-color: var(--ifm-color-primary);
  color: var(--ifm-color-primary-dark);
  border-radius: 9999px;
}

.bp-button--primary:hover {
  background-color: var(--ifm-color-primary-light);
  text-decoration: none;
  color: var(--ifm-color-primary-dark);
}

.bp-button--secondary {
  background-color: transparent;
  color: var(--ifm-color-primary-dark);
  border: 1px solid var(--ifm-color-primary);
  border-radius: 9999px;
}

.bp-button--secondary:hover {
  border-color: var(--ifm-color-primary-dark);
  background-color: var(--ifm-color-primary-lightest);
  text-decoration: none;
  color: var(--ifm-color-primary-dark);
}

.bp-card {
  background-color: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-toc-border-color);
  border-radius: var(--ifm-border-radius);
  padding: var(--ifm-global-spacing);
  transition: all 0.2s ease;
}

.bp-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Animation for loading spinner */
@keyframes bp-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
`;

  return baseCSS + docusaurusVars;
};

// Helper function to apply theme to Docusaurus
export const applyDocusaurusTheme = () => {
  if (typeof document !== 'undefined') {
    const style = document.createElement('style');
    style.textContent = generateDocusaurusCSS();
    document.head.appendChild(style);
  }
};

// Export CSS string for static usage
export const docusaurusCSS = generateDocusaurusCSS();

export default {
  generateDocusaurusCSS,
  applyDocusaurusTheme,
  docusaurusCSS
};
