import { extendTheme, type ThemeConfig } from '@chakra-ui/react';
import { colors, typography, spacing, borderRadius, shadows, transitions } from '../../tokens';

// Chakra UI theme configuration
const config: ThemeConfig = {
  initialColorMode: 'light',
  useSystemColorMode: false,
};

// Convert our design tokens to Chakra format
export const createChakraTheme = () => {
  return extendTheme({
    config,
    
    colors: {
      // Map our color tokens to Chakra format
      primary: {
        50: colors.dataGreenLight,
        100: colors.alpha.primary10,
        200: colors.alpha.primary25,
        300: colors.alpha.primary50,
        400: colors.primaryLight,
        500: colors.primary,
        600: colors.midDataGreen,
        700: colors.primaryDark,
        800: colors.darkGreen,
        900: colors.midnightGreen
      },
      
      gray: {
        50: colors.gray[50],
        100: colors.gray[100],
        200: colors.gray[200],
        300: colors.gray[300],
        400: colors.gray[400],
        500: colors.gray[500],
        600: colors.gray[600],
        700: colors.gray[700],
        800: colors.gray[800],
        900: colors.gray[900]
      },
      
      // Custom color aliases
      dataGreen: colors.primary,
      darkGreen: colors.primaryDark,
      darkBrown: colors.darkBrown,
      desertSand: colors.desertSand,
      beaconSignal: colors.beaconSignal
    },
    
    fonts: {
      heading: typography.fonts.heading,
      body: typography.fonts.body,
      mono: typography.fonts.mono
    },
    
    fontSizes: {
      xs: typography.fontSizes.xs,
      sm: typography.fontSizes.sm,
      md: typography.fontSizes.md,
      lg: typography.fontSizes.lg,
      xl: typography.fontSizes.xl,
      '2xl': typography.fontSizes['2xl'],
      '3xl': typography.fontSizes['3xl'],
      '4xl': typography.fontSizes['4xl'],
      '5xl': typography.fontSizes['5xl'],
      '6xl': typography.fontSizes['6xl'],
      '7xl': typography.fontSizes['7xl'],
      '8xl': typography.fontSizes['8xl'],
      '9xl': typography.fontSizes['9xl']
    },
    
    fontWeights: {
      thin: typography.fontWeights.thin,
      extralight: typography.fontWeights.extralight,
      light: typography.fontWeights.light,
      normal: typography.fontWeights.normal,
      medium: typography.fontWeights.medium,
      semibold: typography.fontWeights.semibold,
      bold: typography.fontWeights.bold,
      extrabold: typography.fontWeights.extrabold,
      black: typography.fontWeights.black
    },
    
    lineHeights: {
      none: typography.lineHeights.none,
      tight: typography.lineHeights.tight,
      snug: typography.lineHeights.snug,
      normal: typography.lineHeights.normal,
      relaxed: typography.lineHeights.relaxed,
      loose: typography.lineHeights.loose
    },
    
    space: {
      0: spacing[0],
      0.5: spacing[0.5],
      1: spacing[1],
      1.5: spacing[1.5],
      2: spacing[2],
      2.5: spacing[2.5],
      3: spacing[3],
      3.5: spacing[3.5],
      4: spacing[4],
      5: spacing[5],
      6: spacing[6],
      7: spacing[7],
      8: spacing[8],
      9: spacing[9],
      10: spacing[10],
      12: spacing[12],
      14: spacing[14],
      16: spacing[16],
      20: spacing[20],
      24: spacing[24],
      28: spacing[28],
      32: spacing[32],
      36: spacing[36],
      40: spacing[40],
      44: spacing[44],
      48: spacing[48],
      52: spacing[52],
      56: spacing[56],
      60: spacing[60],
      64: spacing[64],
      72: spacing[72],
      80: spacing[80],
      96: spacing[96]
    },
    
    radii: {
      none: borderRadius.none,
      sm: borderRadius.sm,
      base: borderRadius.base,
      md: borderRadius.md,
      lg: borderRadius.lg,
      xl: borderRadius.xl,
      '2xl': borderRadius['2xl'],
      '3xl': borderRadius['3xl'],
      full: borderRadius.full
    },
    
    shadows: {
      xs: shadows.xs,
      sm: shadows.sm,
      base: shadows.base,
      md: shadows.md,
      lg: shadows.lg,
      xl: shadows.xl,
      '2xl': shadows['2xl'],
      inner: shadows.inner,
      none: shadows.none,
      glow: shadows.glow,
      glowLarge: shadows.glowLarge,
      dark: shadows.dark
    },
    
    // Component style overrides
    components: {
      Button: {
        baseStyle: {
          fontWeight: typography.fontWeights.medium,
          borderRadius: borderRadius.full,
          transition: transitions.colors
        },
        variants: {
          primary: {
            bg: 'primary.500',
            color: 'primary.700',
            _hover: {
              bg: 'primary.400',
              _disabled: {
                bg: 'primary.500'
              }
            },
            _active: {
              bg: 'primary.600'
            }
          },
          secondary: {
            bg: 'transparent',
            color: 'primary.700',
            border: '1px solid',
            borderColor: 'gray.200',
            _hover: {
              borderColor: 'primary.500',
              bg: 'primary.50'
            }
          },
          ghost: {
            bg: 'transparent',
            color: 'primary.700',
            _hover: {
              bg: 'primary.50'
            }
          }
        },
        sizes: {
          sm: {
            h: 8,
            minW: 8,
            fontSize: 'sm',
            px: 4
          },
          md: {
            h: 10,
            minW: 10,
            fontSize: 'md',
            px: 5
          },
          lg: {
            h: 12,
            minW: 12,
            fontSize: 'lg',
            px: 6
          }
        }
      },
      
      Input: {
        baseStyle: {
          field: {
            borderRadius: borderRadius.md,
            transition: transitions.colors,
            _focus: {
              borderColor: 'primary.500',
              boxShadow: `0 0 0 3px ${colors.alpha.primary15}`
            }
          }
        },
        variants: {
          outline: {
            field: {
              borderColor: 'gray.200',
              _hover: {
                borderColor: 'gray.300'
              }
            }
          }
        }
      },
      
      Card: {
        baseStyle: {
          container: {
            borderRadius: borderRadius.lg,
            boxShadow: shadows.sm,
            transition: transitions.all
          }
        }
      }
    }
  });
};

export default createChakraTheme;
