import React from 'react';
import { colors, spacing, borderRadius, shadows, typography } from '../../../tokens';
import { Typography } from '../../atoms';

export interface CardProps {
  variant?: 'default' | 'feature' | 'post' | 'grid';
  title?: string;
  description?: string;
  image?: string;
  tags?: string[];
  theme?: 'light' | 'dark';
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  variant = 'default',
  title,
  description,
  image,
  tags,
  theme = 'light',
  className = '',
  style,
  children,
  onClick,
  ...props
}) => {
  
  const getVariantStyles = () => {
    const baseStyles = {
      backgroundColor: theme === 'light' ? colors.white : colors.alpha.dark25,
      border: `1px solid ${theme === 'light' ? colors.toastedOat : colors.alpha.desertSand25}`,
      borderRadius: borderRadius.lg,
      overflow: 'hidden' as const,
      transition: 'all 0.2s ease-in-out',
      cursor: onClick ? 'pointer' : 'default'
    };

    switch (variant) {
      case 'feature':
        return {
          ...baseStyles,
          padding: spacing[6],
          textAlign: 'center' as const,
          boxShadow: shadows.sm,
          '&:hover': onClick ? {
            transform: 'translateY(-2px)',
            boxShadow: shadows.md
          } : {}
        };
      
      case 'post':
        return {
          ...baseStyles,
          padding: 0,
          boxShadow: shadows.sm,
          '&:hover': onClick ? {
            boxShadow: shadows.md
          } : {}
        };
      
      case 'grid':
        return {
          ...baseStyles,
          padding: spacing[4],
          boxShadow: shadows.xs,
          '&:hover': onClick ? {
            borderColor: colors.primary,
            boxShadow: shadows.sm
          } : {}
        };
      
      default: // default
        return {
          ...baseStyles,
          padding: spacing[4],
          boxShadow: shadows.xs
        };
    }
  };

  const imageStyles = {
    width: '100%',
    height: variant === 'feature' ? '120px' : variant === 'post' ? '200px' : '150px',
    objectFit: 'cover' as const,
    display: 'block'
  };

  const contentStyles = {
    padding: variant === 'post' ? spacing[4] : 0
  };

  const titleStyles = {
    marginBottom: spacing[2]
  };

  const descriptionStyles = {
    marginBottom: tags && tags.length > 0 ? spacing[3] : 0
  };

  const tagsContainerStyles = {
    display: 'flex',
    flexWrap: 'wrap' as const,
    gap: spacing[2],
    marginTop: spacing[3]
  };

  const tagStyles = {
    fontSize: typography.fontSizes.xs,
    fontWeight: typography.fontWeights.medium,
    padding: `${spacing[1]} ${spacing[2]}`,
    backgroundColor: theme === 'light' ? colors.alpha.primary10 : colors.alpha.primary25,
    color: theme === 'light' ? colors.primary : colors.primaryLight,
    borderRadius: borderRadius.full,
    border: 'none'
  };

  const combinedStyles = {
    ...getVariantStyles(),
    ...style
  };

  return (
    <div
      className={`bp-card bp-card--${variant} ${className}`}
      style={combinedStyles}
      onClick={onClick}
      {...props}
    >
      {image && (
        <img 
          src={image} 
          alt={title || 'Card image'} 
          style={imageStyles}
        />
      )}
      
      <div style={contentStyles}>
        {title && (
          <Typography 
            variant={variant === 'feature' ? 'h4' : 'h5'}
            style={titleStyles}
            color={theme === 'light' ? colors.darkBrown : colors.desertSand}
          >
            {title}
          </Typography>
        )}
        
        {description && (
          <Typography 
            variant="body2"
            style={descriptionStyles}
            color={theme === 'light' ? colors.alpha.dark60 : colors.alpha.desertSand80}
          >
            {description}
          </Typography>
        )}
        
        {children}
        
        {tags && tags.length > 0 && (
          <div style={tagsContainerStyles}>
            {tags.map((tag, index) => (
              <span key={index} style={tagStyles}>
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Card;
