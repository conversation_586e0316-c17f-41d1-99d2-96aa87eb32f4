import React, { forwardRef } from 'react';
import { colors, spacing, typography, borderRadius, transitions } from '../../../tokens';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement> {
  variant?: 'default' | 'contact' | 'email';
  theme?: 'light' | 'dark';
  label?: string;
  error?: string;
  helpText?: string;
  multiline?: boolean;
  rows?: number;
}

const Input = forwardRef<HTMLInputElement | HTMLTextAreaElement, InputProps>(
  ({ 
    variant = 'default',
    theme = 'light',
    label,
    error,
    helpText,
    multiline = false,
    rows = 3,
    className = '',
    style,
    required,
    ...props 
  }, ref) => {
    
    const getVariantStyles = () => {
      const baseInputStyles = {
        width: '100%',
        fontFamily: typography.fonts.body,
        fontSize: typography.fontSizes.md,
        lineHeight: typography.lineHeights.normal,
        transition: transitions.colors,
        outline: 'none',
        '&::placeholder': {
          color: theme === 'light' ? colors.alpha.dark50 : colors.alpha.desertSand50
        }
      };

      switch (variant) {
        case 'contact':
          return {
            ...baseInputStyles,
            padding: `${spacing[3]} ${spacing[4]}`,
            backgroundColor: theme === 'light' ? colors.white : colors.alpha.dark25,
            color: theme === 'light' ? colors.darkBrown : colors.desertSand,
            border: `1px solid ${theme === 'light' ? colors.toastedOat : colors.alpha.desertSand25}`,
            borderRadius: borderRadius.md,
            '&:focus': {
              borderColor: colors.primary,
              boxShadow: `0 0 0 3px ${colors.alpha.primary15}`
            },
            '&:hover': {
              borderColor: theme === 'light' ? colors.alpha.dark30 : colors.alpha.desertSand30
            }
          };
        
        case 'email':
          return {
            ...baseInputStyles,
            padding: `${spacing[3]} ${spacing[4]}`,
            backgroundColor: colors.white,
            color: colors.darkBrown,
            border: 'none',
            borderRadius: `${borderRadius.base} 0 0 ${borderRadius.base}`,
            '&:focus': {
              outline: 'none'
            }
          };
        
        default: // default
          return {
            ...baseInputStyles,
            padding: `${spacing[3]} ${spacing[4]}`,
            backgroundColor: theme === 'light' ? colors.white : colors.alpha.dark25,
            color: theme === 'light' ? colors.darkBrown : colors.desertSand,
            border: `1px solid ${theme === 'light' ? colors.gray[200] : colors.alpha.desertSand25}`,
            borderRadius: borderRadius.md,
            '&:focus': {
              borderColor: colors.primary,
              boxShadow: `0 0 0 3px ${colors.alpha.primary15}`
            },
            '&:hover': {
              borderColor: theme === 'light' ? colors.gray[300] : colors.alpha.desertSand30
            }
          };
      }
    };

    const labelStyles = {
      display: 'block',
      fontSize: typography.fontSizes.sm,
      fontWeight: typography.fontWeights.medium,
      color: theme === 'light' ? colors.darkBrown : colors.desertSand,
      marginBottom: spacing[2],
      fontFamily: typography.fonts.mono
    };

    const errorStyles = {
      fontSize: typography.fontSizes.sm,
      color: colors.error,
      marginTop: spacing[1],
      fontFamily: typography.fonts.body
    };

    const helpTextStyles = {
      fontSize: typography.fontSizes.sm,
      color: theme === 'light' ? colors.alpha.dark60 : colors.alpha.desertSand80,
      marginTop: spacing[1],
      fontFamily: typography.fonts.body
    };

    const inputStyles = {
      ...getVariantStyles(),
      ...(error && {
        borderColor: colors.error,
        '&:focus': {
          borderColor: colors.error,
          boxShadow: `0 0 0 3px ${colors.alpha.primary15}`
        }
      }),
      ...style
    };

    const InputComponent = multiline ? 'textarea' : 'input';

    return (
      <div className={`bp-input-wrapper ${className}`}>
        {label && (
          <label style={labelStyles}>
            {label}
            {required && (
              <span 
                style={{ 
                  color: colors.error, 
                  marginLeft: spacing[1] 
                }}
              >
                *
              </span>
            )}
          </label>
        )}
        
        <InputComponent
          ref={ref as any}
          className={`bp-input bp-input--${variant}`}
          style={inputStyles}
          rows={multiline ? rows : undefined}
          {...(props as any)}
        />
        
        {error && (
          <div style={errorStyles} className="bp-input-error">
            {error}
          </div>
        )}
        
        {helpText && !error && (
          <div style={helpTextStyles} className="bp-input-help">
            {helpText}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
