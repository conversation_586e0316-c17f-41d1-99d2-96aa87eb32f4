import React, { forwardRef } from 'react';
import { colors, spacing, typography, borderRadius, transitions } from '../../../tokens';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  theme?: 'light' | 'dark';
  loading?: boolean;
  children: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    variant = 'primary', 
    size = 'md', 
    theme = 'light',
    loading = false,
    disabled,
    children, 
    className = '',
    style,
    ...props 
  }, ref) => {
    
    const getVariantStyles = () => {
      switch (variant) {
        case 'primary':
          return {
            backgroundColor: colors.primary,
            color: colors.primaryDark,
            border: 'none',
            '&:hover': {
              backgroundColor: colors.primaryLight
            },
            '&:active': {
              backgroundColor: colors.midDataGreen
            }
          };
        
        case 'secondary':
          return {
            backgroundColor: 'transparent',
            color: theme === 'light' ? colors.primaryDark : colors.primary,
            border: `1px solid ${theme === 'light' ? colors.alpha.dark25 : colors.alpha.primary25}`,
            '&:hover': {
              borderColor: theme === 'light' ? colors.primaryDark : colors.primary,
              backgroundColor: theme === 'light' ? colors.alpha.primary10 : colors.alpha.dark25
            }
          };
        
        case 'ghost':
          return {
            backgroundColor: 'transparent',
            color: theme === 'light' ? colors.primaryDark : colors.primary,
            border: 'none',
            '&:hover': {
              backgroundColor: theme === 'light' ? colors.alpha.primary10 : colors.alpha.dark25
            }
          };
        
        case 'icon':
          return {
            backgroundColor: 'transparent',
            color: theme === 'light' ? colors.primaryDark : colors.primary,
            border: `1px solid ${colors.alpha.primary25}`,
            borderRadius: borderRadius.md,
            padding: spacing[3],
            minWidth: 'auto',
            aspectRatio: '1',
            '&:hover': {
              borderColor: colors.primary,
              backgroundColor: colors.alpha.primary10
            }
          };
        
        default:
          return {};
      }
    };

    const getSizeStyles = () => {
      switch (size) {
        case 'sm':
          return {
            padding: variant === 'icon' ? spacing[2] : `${spacing[2]} ${spacing[4]}`,
            fontSize: typography.fontSizes.sm,
            minHeight: spacing[8]
          };
        
        case 'lg':
          return {
            padding: variant === 'icon' ? spacing[4] : `${spacing[4]} ${spacing[6]}`,
            fontSize: typography.fontSizes.lg,
            minHeight: spacing[12]
          };
        
        default: // md
          return {
            padding: variant === 'icon' ? spacing[3] : `${spacing[3]} ${spacing[5]}`,
            fontSize: typography.fontSizes.md,
            minHeight: spacing[10]
          };
      }
    };

    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: spacing[2],
      fontFamily: typography.fonts.body,
      fontWeight: typography.fontWeights.medium,
      lineHeight: typography.lineHeights.tight,
      borderRadius: variant === 'icon' ? borderRadius.md : borderRadius.full,
      cursor: disabled || loading ? 'not-allowed' : 'pointer',
      transition: transitions.colors,
      textDecoration: 'none',
      outline: 'none',
      position: 'relative' as const,
      overflow: 'hidden',
      opacity: disabled || loading ? 0.6 : 1,
      ...getSizeStyles(),
      ...getVariantStyles()
    };

    const combinedStyle = {
      ...baseStyles,
      ...style
    };

    return (
      <button
        ref={ref}
        className={`bp-button bp-button--${variant} bp-button--${size} ${className}`}
        style={combinedStyle}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <span 
            style={{
              display: 'inline-block',
              width: typography.fontSizes.sm,
              height: typography.fontSizes.sm,
              border: `2px solid ${variant === 'primary' ? colors.primaryDark : colors.primary}`,
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'bp-spin 1s linear infinite',
              marginRight: children ? spacing[2] : 0
            }}
          />
        )}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
