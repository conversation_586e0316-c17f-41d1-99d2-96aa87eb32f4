import React from 'react';
import { colors, typography } from '../../../tokens';

export interface TypographyProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'mono1' | 'mono2' | 'overline' | 'button';
  color?: string;
  align?: 'left' | 'center' | 'right' | 'justify';
  component?: keyof JSX.IntrinsicElements;
  className?: string;
  style?: React.CSSProperties;
  children: React.ReactNode;
}

const Typography: React.FC<TypographyProps> = ({
  variant = 'body1',
  color,
  align = 'left',
  component,
  className = '',
  style,
  children,
  ...props
}) => {
  
  const getDefaultComponent = (): keyof JSX.IntrinsicElements => {
    switch (variant) {
      case 'h1':
      case 'h2':
      case 'h3':
      case 'h4':
      case 'h5':
      case 'h6':
        return variant;
      case 'caption':
      case 'overline':
        return 'span';
      default:
        return 'p';
    }
  };

  const Component = component || getDefaultComponent();

  const getVariantStyles = () => {
    const textStyle = typography.textStyles[variant];
    if (!textStyle) return {};

    return {
      fontSize: textStyle.fontSize,
      fontWeight: textStyle.fontWeight,
      lineHeight: textStyle.lineHeight,
      fontFamily: textStyle.fontFamily === 'heading' 
        ? typography.fonts.heading 
        : textStyle.fontFamily === 'mono' 
          ? typography.fonts.mono 
          : typography.fonts.body,
      letterSpacing: textStyle.letterSpacing || 'normal',
      textTransform: textStyle.textTransform || 'none'
    };
  };

  const getColorValue = () => {
    if (color) {
      // Check if it's a token reference
      if (color.startsWith('colors.')) {
        const colorPath = color.replace('colors.', '');
        const colorKeys = colorPath.split('.');
        let colorValue: any = colors;
        
        for (const key of colorKeys) {
          colorValue = colorValue[key];
          if (colorValue === undefined) break;
        }
        
        return typeof colorValue === 'string' ? colorValue : color;
      }
      return color;
    }
    
    // Default colors based on variant
    switch (variant) {
      case 'h1':
      case 'h2':
      case 'h3':
      case 'h4':
      case 'h5':
      case 'h6':
        return colors.darkBrown;
      case 'caption':
        return colors.alpha.dark60;
      case 'overline':
        return colors.alpha.dark60;
      case 'mono1':
      case 'mono2':
        return colors.darkBrown;
      default:
        return colors.darkBrown;
    }
  };

  const baseStyles: React.CSSProperties = {
    margin: 0,
    padding: 0,
    textAlign: align,
    color: getColorValue(),
    ...getVariantStyles()
  };

  const combinedStyles = {
    ...baseStyles,
    ...style
  };

  return (
    <Component
      className={`bp-typography bp-typography--${variant} ${className}`}
      style={combinedStyles}
      {...props}
    >
      {children}
    </Component>
  );
};

export default Typography;
