export * from './colors';
export * from './typography';
export * from './spacing';

// Re-export all tokens as a single object for convenience
import { colors } from './colors';
import { typography } from './typography';
import { spacing, borderRadius, breakpoints, zIndex, shadows, transitions } from './spacing';

export const tokens = {
  colors,
  typography,
  spacing,
  borderRadius,
  breakpoints,
  zIndex,
  shadows,
  transitions
} as const;

// CSS Custom Properties generator
export const generateCSSVariables = () => {
  const cssVars: Record<string, string> = {};
  
  // Colors
  Object.entries(colors).forEach(([key, value]) => {
    if (typeof value === 'string') {
      cssVars[`--bp-color-${key}`] = value;
    } else if (typeof value === 'object') {
      Object.entries(value).forEach(([subKey, subValue]) => {
        if (typeof subValue === 'string') {
          cssVars[`--bp-color-${key}-${subKey}`] = subValue;
        }
      });
    }
  });
  
  // Typography
  Object.entries(typography.fonts).forEach(([key, value]) => {
    cssVars[`--bp-font-${key}`] = value;
  });
  
  Object.entries(typography.fontSizes).forEach(([key, value]) => {
    cssVars[`--bp-font-size-${key}`] = value;
  });
  
  Object.entries(typography.fontWeights).forEach(([key, value]) => {
    cssVars[`--bp-font-weight-${key}`] = value.toString();
  });
  
  Object.entries(typography.lineHeights).forEach(([key, value]) => {
    cssVars[`--bp-line-height-${key}`] = value.toString();
  });
  
  // Spacing
  Object.entries(spacing).forEach(([key, value]) => {
    cssVars[`--bp-space-${key}`] = value;
  });
  
  // Border Radius
  Object.entries(borderRadius).forEach(([key, value]) => {
    cssVars[`--bp-radius-${key}`] = value;
  });
  
  // Shadows
  Object.entries(shadows).forEach(([key, value]) => {
    cssVars[`--bp-shadow-${key}`] = value;
  });
  
  // Transitions
  Object.entries(transitions).forEach(([key, value]) => {
    cssVars[`--bp-transition-${key}`] = value;
  });
  
  return cssVars;
};

// Generate CSS string
export const generateCSSString = () => {
  const cssVars = generateCSSVariables();
  const cssString = Object.entries(cssVars)
    .map(([property, value]) => `  ${property}: ${value};`)
    .join('\n');
  
  return `:root {\n${cssString}\n}`;
};

// Theme object for JavaScript usage
export const theme = {
  ...tokens,
  
  // Helper functions
  getColor: (path: string) => {
    const keys = path.split('.');
    let value: any = colors;
    
    for (const key of keys) {
      value = value[key];
      if (value === undefined) return undefined;
    }
    
    return value;
  },
  
  getSpacing: (key: keyof typeof spacing) => spacing[key],
  
  getFontSize: (key: keyof typeof typography.fontSizes) => typography.fontSizes[key],
  
  getBreakpoint: (key: keyof typeof breakpoints) => breakpoints[key],
  
  getShadow: (key: keyof typeof shadows) => shadows[key],
  
  getTransition: (key: keyof typeof transitions) => transitions[key]
} as const;

export type Theme = typeof theme;
