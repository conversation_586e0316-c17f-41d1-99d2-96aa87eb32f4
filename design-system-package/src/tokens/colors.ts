export const colors = {
  // Brand Colors
  primary: '#19DFAE',
  primaryDark: '#071F21',
  primaryLight: '#5FF6CC',
  
  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  
  // Gray Scale
  gray: {
    50: '#F9F5F1',
    100: '#F2EAE2',
    200: '#E5D3C3',
    300: '#D1A8D5',
    400: '#9B9BA1',
    500: '#311D19',
    600: '#2D1901',
    700: '#071F21',
    800: '#003235',
    900: '#000000'
  },
  
  // Semantic Colors
  success: '#19DFAE',
  warning: '#EF992F',
  error: '#C63131',
  info: '#19BADF',
  
  // Extended Palette
  dataGreen: '#19DFAE',
  dataGreenLight: '#E8FAF5',
  dataGreenBright: '#5FF6CC',
  midDataGreen: '#2AD19F',
  
  darkGreen: '#071F21',
  midnightGreen: '#003235',
  midnightGreenLight: '#03514E',
  nightGreen: '#034C51',
  deepGreen: '#00272C',
  
  // Browns
  darkBrown: '#311D19',
  roseBrown: '#E59B7B',
  
  // Whites & Creams
  syncLight: '#F9F5F1',
  cloudSync: '#F2EAE2',
  desertSand: '#F9F5F1',
  ivoryDust: '#ECE0D4',
  almondCream: '#F2EAE2',
  toastedOat: '#E5D3C3',
  
  // Accent Colors
  yellowGold: '#EBC800',
  goldenSunset: '#EF992F',
  beaconSignal: '#EF992F',
  
  // Tertiary Colors
  lightOrchid: '#D1A8D5',
  orchid: '#C678DD',
  purple: '#B84DFF',
  darkOrchid: '#BD6AC5',
  darkPurple: '#482251',
  darkerPurple: '#361A3D',
  blue: '#19BADF',
  darkBlue: '#185F99',
  denimBlue: '#8CC4F2',
  teal: '#19DFAE',
  lime: '#9DDF19',
  lemonYellow: '#F6E36D',
  yellow: '#DFC419',
  orange: '#E59B7B',
  persimmon: '#C63131',
  burgundy: '#350001',
  darkBurgundy: '#2D1901',
  
  // Alpha Variants
  alpha: {
    primary10: 'rgba(25, 223, 174, 0.1)',
    primary15: 'rgba(25, 223, 174, 0.15)',
    primary25: 'rgba(25, 223, 174, 0.25)',
    primary30: 'rgba(25, 223, 174, 0.3)',
    primary50: 'rgba(25, 223, 174, 0.5)',
    
    dark25: 'rgba(7, 31, 33, 0.25)',
    dark30: 'rgba(7, 31, 33, 0.3)',
    dark50: 'rgba(7, 31, 33, 0.5)',
    dark60: 'rgba(7, 31, 33, 0.6)',
    dark90: 'rgba(7, 31, 33, 0.9)',
    
    desertSand25: 'rgba(249, 245, 241, 0.25)',
    desertSand30: 'rgba(249, 245, 241, 0.30)',
    desertSand50: 'rgba(249, 245, 241, 0.5)',
    desertSand80: 'rgba(249, 245, 241, 0.8)',
    
    toastedOat35: 'rgba(229, 211, 195, 0.35)',
    darkBrown30: 'rgba(49, 29, 25, 0.3)',
    darkBrown80: 'rgba(49, 29, 25, 0.8)',
    darkBrown4: 'rgba(49, 29, 25, 0.04)',
    
    midnightGreen50: 'rgba(0, 50, 53, 0.5)'
  },
  
  // Linear Gradients
  gradients: {
    toastedOat: 'linear-gradient(0deg, #E5D3C3 0%, #E5D3C3 100%)',
    blockstream: 'linear-gradient(30deg, #071F21 30%, #071F21 100%)',
    black: 'linear-gradient(30deg, #000000 100%)',
    tealBlue: 'linear-gradient(30deg, #092A2C 100%)',
    aquaBlue: 'linear-gradient(30deg, #19BDBF 25%, #19DFAE 30%)',
    limeGreen: 'linear-gradient(30deg, #9DDF19 100%)',
    beaconSignal: 'linear-gradient(10deg, #EF992F 100%)',
    deepBlue: 'linear-gradient(30deg, #00252B 100%)',
    navyBlue: 'linear-gradient(30deg, #03323A 100%)',
    midnightBlue: 'linear-gradient(30deg, #04404A 100%)',
    forestGreen: 'linear-gradient(30deg, #0A2F31 100%)',
    brownDark: 'linear-gradient(30deg, #2D1901 100%)',
    deepLedger: 'linear-gradient(30deg, #361A3D 100%)',
    nodePulse: 'linear-gradient(30deg, #D1A8D5 100%)',
    grayDark: 'linear-gradient(30deg, #9B9BA1 100%)',
    lavender: 'linear-gradient(25deg, #BD6AC5 100%)',
    plum: 'linear-gradient(25deg, #D1A8D5 100%)',
    rosePink: 'linear-gradient(25deg, #E08185 100%)',
    beaconSignal25: 'linear-gradient(25deg, #EF992F 100%)',
    yellowGreen: 'linear-gradient(25deg, #F6E36D 100%)',
    syncLight: 'linear-gradient(25deg, #F9F5F1 100%)'
  }
} as const;

export type ColorToken = keyof typeof colors;
export type GrayToken = keyof typeof colors.gray;
export type AlphaToken = keyof typeof colors.alpha;
export type GradientToken = keyof typeof colors.gradients;
