export const typography = {
  fonts: {
    body: 'Figtree, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif',
    heading: '<PERSON><PERSON>ry Medium Pro, Figtree, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif',
    mono: 'MonaspaceNeon, Neon-Regular, ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
  },
  
  fontSizes: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    md: '1rem',        // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
    '7xl': '4.5rem',   // 72px
    '8xl': '6rem',     // 96px
    '9xl': '8rem'      // 128px
  },
  
  fontWeights: {
    thin: 100,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900
  },
  
  lineHeights: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2
  },
  
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em'
  },
  
  // Text Styles (combinations of font properties)
  textStyles: {
    // Headings
    h1: {
      fontSize: '3rem',
      fontWeight: 600,
      lineHeight: 1.25,
      fontFamily: 'heading'
    },
    h2: {
      fontSize: '2.25rem',
      fontWeight: 600,
      lineHeight: 1.25,
      fontFamily: 'heading'
    },
    h3: {
      fontSize: '1.875rem',
      fontWeight: 500,
      lineHeight: 1.375,
      fontFamily: 'heading'
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.375,
      fontFamily: 'heading'
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'heading'
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'heading'
    },
    
    // Body Text
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: 'body'
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: 'body'
    },
    body1Medium: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'body'
    },
    body2Medium: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'body'
    },
    body3Medium: {
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'body'
    },
    
    // Mono Text
    mono1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: 'mono'
    },
    mono1Medium: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'mono'
    },
    mono2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: 'mono'
    },
    mono2Medium: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'mono'
    },
    mono3Medium: {
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: 1.5,
      fontFamily: 'mono'
    },
    
    // Special Text
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: 'body'
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: '0.1em',
      textTransform: 'uppercase',
      fontFamily: 'body'
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.25,
      fontFamily: 'body'
    },
    link: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: 'body',
      textDecoration: 'underline'
    }
  }
} as const;

export type FontFamily = keyof typeof typography.fonts;
export type FontSize = keyof typeof typography.fontSizes;
export type FontWeight = keyof typeof typography.fontWeights;
export type LineHeight = keyof typeof typography.lineHeights;
export type LetterSpacing = keyof typeof typography.letterSpacing;
export type TextStyle = keyof typeof typography.textStyles;
