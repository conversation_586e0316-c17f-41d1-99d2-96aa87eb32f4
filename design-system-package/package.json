{"name": "@blockparty/design-system", "version": "1.0.0", "description": "Shared design system for Blockparty projects", "private": true, "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./tokens": {"import": "./dist/tokens/index.esm.js", "require": "./dist/tokens/index.js", "types": "./dist/tokens/index.d.ts"}, "./components": {"import": "./dist/components/index.esm.js", "require": "./dist/components/index.js", "types": "./dist/components/index.d.ts"}, "./adapters/docusaurus": {"import": "./dist/adapters/docusaurus/index.esm.js", "require": "./dist/adapters/docusaurus/index.js", "types": "./dist/adapters/docusaurus/index.d.ts"}, "./adapters/chakra": {"import": "./dist/adapters/chakra/index.esm.js", "require": "./dist/adapters/chakra/index.js", "types": "./dist/adapters/chakra/index.d.ts"}, "./styles": "./dist/styles/index.css"}, "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "dev": "rollup -c -w", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@babel/preset-react": "^7.22.0", "@babel/preset-typescript": "^7.23.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@storybook/addon-essentials": "^7.5.0", "@storybook/addon-interactions": "^7.5.0", "@storybook/addon-links": "^7.5.0", "@storybook/blocks": "^7.5.0", "@storybook/react": "^7.5.0", "@storybook/react-vite": "^7.5.0", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "rimraf": "^5.0.5", "rollup": "^4.3.0", "rollup-plugin-postcss": "^4.0.2", "storybook": "^7.5.0", "typescript": "^5.2.2"}, "optionalDependencies": {"@chakra-ui/react": "^2.8.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0"}, "keywords": ["design-system", "react", "components", "ui", "blockparty"], "author": "Blockparty Team", "license": "UNLICENSED", "repository": {"type": "git", "url": "git+https://github.com/blockparty/design-system.git"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}