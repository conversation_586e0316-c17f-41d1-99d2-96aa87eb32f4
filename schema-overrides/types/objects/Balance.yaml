Balance:
  description: |
    The `Balance` object represents a user's or contract's holdings of a specific token/NFT. This object provides crucial ownership details, including the token amount held, associated smart contract information, and token metadata where applicable.
    ### Example Query

    ```graphql
    query GetBalance($owner: ID!, $contract: ID!) {
      balance(owner: $owner, contract: $contract) {
        value
        token {
          name
        }
        contractType
        owner
        contract {
          decimals
        }
      }
    }
    ```

    ### Understanding the `value` Field

    The `value` field returns `ERC-20` token balances **exactly as stored on the Ethereum blockchain** - as a raw, unformatted integer string representing the smallest unit of the token. This preserves full precision without decimal approximation.

    You will need to convert the `value` to its decimal format using the token's `decimals`.

    ```javascript
    // Requires knowing the token's decimals
    const formatted = value / (10 ** tokenDecimals);
    ```

    The **ether.js** and **web3.js** libraries can also help with these conversions.

    ### Use Cases

    * Checking ownership of specific NFTs.
    * Calculating total holdings of a particular token.
    * Building portfolio dashboards.

    ### Implementation Notes

    * The `value` field returns strings to support arbitrary precision.
    * For ERC-20 tokens, `token` will be null.
    * Use with the `Balances` type for paginated results.
    * Combine with the `Tokens` type to get complete metadata when displaying NFT collections.

    ### Query Format
  fields:
    owner:
      description: The address of the owner of the balance.
    contractType:
      description: The type of contract (`ERC-721`, `ERC-1155`, `ERC-20`).
    contract:
      description: The contract object.
    token:
      description: The token object (if applicable).
    value:
      description: The balance value.
