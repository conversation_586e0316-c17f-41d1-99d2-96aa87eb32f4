Contracts:
  description: |
    The `Contracts` object provides a **paginated, cursor-based list** of smart contracts (`Contract` objects). It enables efficient traversal of large contract datasets while tracking pagination state via `cursor` and `remaining` fields.
    ### Example Query

    ```graphql
    query Contracts($first: Int, $where: ContractFilter, $orderBy: ContractOrderBy, $orderDirection: OrderDirection) {
      contracts(first: $first, where: $where, orderBy: $orderBy, orderDirection: $orderDirection) {
        contracts {
          symbol
          name
          createdAt
          id
        }
      }
    }
    ```

    ### Use Cases

    * Contract discovery (browse all contracts on a given chain and filter by type).
    * Analytics dashboards (track holder distribution and contract deployment).
    * Multi-Contract UIs (display ranked lists on a marketplace frontend).

    ### Query Format
  fields:
    count:
      description: Total contracts matching query.
    remaining:
      description: Contracts left to fetch.
    cursor:
      description: Pagination key (null if no more results).
    contracts:
      description: List of contracts with full details.
