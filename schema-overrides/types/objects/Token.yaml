Token:
  description: |
    The `Token` object represents a **single NFT**. It provides comprehensive on-chain data, metadata, ownership details, and lifecycle events.

    ### Example Query

    ```graphql
    query Query($contract: ID!, $tokenId: ID!) {
      token(contract: $contract, tokenID: $tokenId) {
        metadataContent
        name
        tokenID
        tokenURI
        description
        holders {
          balances {
            owner
          }
        }
      }
    }
    ```

    ### Use Cases

    * NFT Marketplaces (show NFT details like image, name, description).
    * Wallet Dashboards (retrieve metadata and NFT media for display).
    * Token Analytics (track mint/burn rates across collections).

    ### Query Format
  fields:
    contract:
      description: The contract that minted this token.
    tokenID:
      description: The token ID.
    tokenURI:
      description: The token URI.
    tokenURIStatus:
      description: The status of the token URI.
    image:
      description: The image associated with this token.
    metadataStatus:
      description: The status of the metadata.
    metadataContent:
      description: The metadata content.
    metadataContentType:
      description: The content type of the metadata.
    createdAt:
      description: When the token was created.
    createdBlock:
      description: The block number at which the token was created.
    burnedAt:
      description: When the token was burned (if applicable).
    burnedBlock:
      description: The block number at which the token was burned (if applicable).
    errorMsg:
      description: Error message (if any).
    name:
      description: The name of the token.
    description:
      description: The description of the token.
    expired:
      description: Whether the token has expired.
    dexTrades:
      description: Decentralized exchange trades involving this token.
    holders:
      description: The quantity of holders of this token.
