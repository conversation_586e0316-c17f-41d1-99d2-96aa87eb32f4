Balances:
  description: |
    The `Balances` object provides a **paginated, cursor-based list** of token/NFT holdings (`Balance` objects) for a given user or contract address. It enables efficient traversal of large balance datasets while tracking pagination state via `cursor` and `remaining` fields.
    ### Example Query

    ```graphql
    query GetBalances($owner: String!, $cursor: String) {
      balances(owner: $owner, first: 10, after: $cursor) {
        balances { owner, value, contract { id } }
        remaining
        cursor
      }
    }
    ```

    ### Use Cases

    * Displaying all owned NFTs/tokens.
    * Calculating holdings across a collection.
    * Paginated UIs (lazy-loading balances in chunks).

    ### Query Format
  fields:
    count:
      description: Total balances matching the query.
    remaining:
      description: Count of balances not yet returned in the current query.
    cursor:
      description: Pagination key for fetching the next batch (null if no more results).
    balances:
      description: List of individual token balances.
