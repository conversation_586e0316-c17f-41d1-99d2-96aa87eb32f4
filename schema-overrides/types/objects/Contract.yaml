Contract:
  description: |
    The `Contract` object represents a contract address deployed on-chain. It contains comprehensive details about the smart contract.

    ### Example Query

    ```graphql
    query AuditContract($contractId: ID!) {
      contract(id: $contractId) {
        id
        type
        name
        symbol
        holders
        createdAt
      }
    }
    ```

    ### Use Cases

    * Contract audits (fetch comprehensive details about a contract).
    * Track total holders for community growth metrics.
    * NFT discovery (filter contracts by token standard, name, etc.).

    ### Query Format
  fields:
    id:
      description: The contract address.
    type:
      description: The type of contract.
    isERC20:
      description: Whether the contract is an `ERC-20` contract.
    isERC721:
      description: Whether the contract is an `ERC-721` contract.
    isERC1155:
      description: Whether the contract is an `ERC-1155` contract.
    name:
      description: The name of the contract.
    symbol:
      description: The symbol of the contract.
    decimals:
      description: The number of decimals (for `ERC-20`).
    supportsMetadata:
      description: Whether the contract supports metadata.
    createdAt:
      description: When the contract was created.
    createdBlock:
      description: The block number at which the contract was created.
    holders:
      description: The number of holders.
    tokens:
      description: The tokens in this contract.
