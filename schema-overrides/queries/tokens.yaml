Query:
  fields:
    tokens:
      description: |
        The `tokens` query retrieves a paginated list of NFT/token assets with flexible filtering and sorting. This is the primary method for fetching multiple tokens from collections or across the entire index.

        ### Example

        #### Fetch First 10 Collection Tokens

        ```graphql
        query Tokens($first: Int, $whereContract: ContractFilter, $orderBy: TokenOrderBy) {
          tokens(first: $first, whereContract: $whereContract, orderBy: $orderBy) {
            tokens {
              tokenID
              image {
                url
              }
              name
            }
          }
        }
        ```

        #### Variables

        ```json
        {
          "first": "10",
          "whereContract": {
            "id": "******************************************"
          },
          "orderBy": "TOKEN_ID"
        }
        ```

        #### Response

        This response is an example only and may not reflect current data.

        ```json
        {
          "data": {
            "tokens": {
              "tokens": [
                {
                  "tokenID": "1",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/1.png"
                  },
                  "name": "CloneX #1"
                },
                {
                  "tokenID": "2",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/2.png"
                  },
                  "name": "CloneX #2"
                },
                {
                  "tokenID": "3",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/3.png"
                  },
                  "name": "CloneX #3"
                },
                {
                  "tokenID": "4",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/4.png"
                  },
                  "name": "CloneX #4"
                },
                {
                  "tokenID": "5",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/5.png"
                  },
                  "name": "CloneX #5"
                },
                {
                  "tokenID": "6",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/6.png"
                  },
                  "name": "CloneX #6"
                },
                {
                  "tokenID": "7",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/7.png"
                  },
                  "name": "CloneX #7"
                },
                {
                  "tokenID": "8",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/8.png"
                  },
                  "name": "CloneX #8301"
                },
                {
                  "tokenID": "9",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/9.png"
                  },
                  "name": "CloneX #1174"
                },
                {
                  "tokenID": "10",
                  "image": {
                    "url": "https://clonex-assets.rtfkt.com/images/10.png"
                  },
                  "name": "CloneX #19995"
                }
              ]
            }
          }
        }
        ```

        ### Common Use Cases

        1. **Collection Browsing**: Display tokens from a specific contract.
        2. **Marketplace Listings**: Show filtered tokens for sale.
        3. **Data Analysis**: Study token distributions/metadata.
        4. **Wallet Integration**: Load all tokens owned by an address.

        ### Best Practices

        * Always include `first` to limit response size.
        * Use `where` filters before sorting for better performance.
        * Request `count` only when needed for UI pagination.
        * Combine with `contract` query for collection context.

        ### Related Queries

        * `token`: For single token lookups.
        * `contract`: To verify collection details.

        ### Query Format
      args:
        first: Default value is 10. Number of tokens to return.
        skip: Default value is 0. Legacy pagination offset.
        after: Cursor for next page.
        where: Filter by `metadataStatus`, `burned`, `name_like`, etc.
        orderBy: "`CONTRACT`, `COLLECTION_HOLDERS`, `TOKEN_ID`, `NAME`, `CREATED_AT`,
          `CREATED_BLOCK` "
        orderDirection: "`ASC`, `DESC`   "
        whereContract: Filter by contract properties `id`, `name`, `type`, etc.
        whereSwap: ""
