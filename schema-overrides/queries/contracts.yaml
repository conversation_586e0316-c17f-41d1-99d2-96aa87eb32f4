Query:
  fields:
    contracts:
      description: |
        The `contracts` query retrieves a paginated list of smart contracts with filtering and sorting capabilities. This is your primary tool for discovering and analyzing NFT collections, token contracts, and their metadata across the blockchain.

        ### Example

        #### 10 Newest ERC-1155 Contracts

        ```graphql
        query Contracts($first: Int, $where: ContractFilter, $orderBy: ContractOrderBy, $orderDirection: OrderDirection) {
          contracts(first: $first, where: $where, orderBy: $orderBy, orderDirection: $orderDirection) {
            contracts {
              symbol
              name
              createdAt
              id
            }
          }
        }
        ```

        #### Variables

        ```json
        {
          "first": "10",
          "where": {
            "is_erc1155": true
          },
          "orderBy": "CREATED_AT",
          "orderDirection": "DESC"
        }
        ```

        #### Response

        This response is an example only and may not reflect current data.

        ```json
        {
          "data": {
            "contracts": {
              "contracts": [
                {
                  "symbol": "BZZ",
                  "name": "BUZZ",
                  "createdAt": "2025-03-14T13:19:35Z",
                  "id": "0xce3a9dd08d4ec23efe955549ce82a2d9485c6c70"
                }
            ]
            }
          }
        }
        ```

        ### Common Use Cases

        1. **Collection Discovery**: Browse trending NFT projects.
        2. **Developer Onboarding**: Find contracts for integration.
        3. **Analytics**: Track contract deployment trends.
        4. **Metadata Research**: Identify contracts with metadata support.

        ### Best Practices

        * Use `where` filters to improve performance.
        * Prefer `after` over `skip` for large datasets.
        * Cache frequently accessed contract lists.
        * Combine with `tokens` query for full collection analysis.

        ### Related Queries

        * `contract`: For single contract details.
        * `tokens`: To explore a contract's NFTs.

        ### Query Format
      args:
        first: Default value is 10. Number of contracts to return.
        skip: Default value is 0. Legacy pagination offset.
        after: Cursor for next page.
        where: Filter by `is_erc20`, `is_erc721`, `is_erc1155`, `name_like`, etc.
        orderBy: "`ID`, `NAME`, `SYMBOL`, `CREATED_AT`, `CREATED_BLOCK`"
        orderDirection: "`ASC`, `DESC` "
