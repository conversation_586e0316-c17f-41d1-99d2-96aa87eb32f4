Query:
  fields:
    balance:
      description: |
        The `balance` query retrieves **a specific token/NFT balance** for a given user or contract address. It returns
        a single `Balance` object matching the provided filters.

        ### Example Query

        #### Fetch ERC-1155 Balanc

        ```graphql

        query GetNFTBalance($contract: ID!, $tokenId: String, $owner: ID!) {
          balance(contract: $contract, tokenID: $tokenId, owner: $owner) {
            owner
            value
            contract {
              name
              symbol
            }
            token {
              name
              tokenID
              description
            }
          }
        }

        ```

        #### Variables

        ```json

        {
          "owner": "0x4182A46C61C3EE40E61304F8B419F813Eeced3b4",
          "contract": "0x76be3b62873462d2142405439777e971754e8e77",
          "tokenId": "10769"
        }

        ```

        #### Response

        This response is an example only and may not reflect current data.

        ```json

        {
          "data": {
            "balance": {
              "owner": "0x4182a46c61c3ee40e61304f8b419f813eeced3b4",
              "value": "6",
              "contract": {
                "name": "parallel",
                "symbol": "LL"
              },
              "token": {
                "name": "<PERSON>, <PERSON>ur<PERSON>or of Curiosities",
                "tokenID": "10769",
                "description": "I can offer you unlimited knowledge and worldly pleasures. Once a upon a time this stuff used to cost a lot more than credits"
              }
            }
          }
        }

        ```

        ### Common Use Cases

        1. Verifying NFT ownership before allowing actions.
        2. Checking token balances for wallet displays.
        3. Validating fractional ownership (ERC-1155). 
        4. Building allow lists based on token holdings.

        ### Best Practices

        * Always include at least `owner` and `contract` arguments.
        * For ERC-1155 tokens, check the `value` field for quantity owned.
        * Combine with `contractType` filter when you only need specific token standards.
        * Returns `null` when no matching balance is found.
        * Returns an error if required arguments (`owner`, `contract`) are missing.
        * May return partial data if metadata is unavailable.

        ### Related Queries

        * `balances`: For paginated lists of all holdings.
        * `contract`: To verify token standards before querying balances.

        ### Query Format
      args:
        owner: The user or contract address being checked.
        contract: The contract address of the token or NFT.
        tokenID: Specific token identifier (for `ERC-721` and `ERC-1155` contracts).
          Used when querying a particular NFT in a collection.
        contractType: Filter by token standard (`ERC-20`, `ERC-721`, `ERC-1155`). Used
          when you only want balances of a specific token type.
