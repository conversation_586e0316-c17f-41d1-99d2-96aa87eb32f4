Query:
  fields:
    contract:
      description: |
        The `contract` query retrieves detailed information about a single smart contract, including its token standard (`ERC-721`/`ERC-1155`/`ERC-20`), metadata capabilities, and deployment details. This is essential for verifying contract properties before interacting with its tokens.

        ### Example

        #### Basic Contract Verification

        ```graphql
        query GetContractDetails($id: ID!) {
          contract(id: $id) {
            id
            name
            symbol
            isERC721
            isERC1155
            supportsMetadata
            createdAt
          }
        }
        ```

        #### Variables

        ```json
        {
          "id": "0xbc4ca0eda7647a8ab7c2061c2e118a18a936f13d"
        }
        ```

        #### Response

        ```json
        {
          "data": {
            "contract": {
              "id": "0xbc4ca0eda7647a8ab7c2061c2e118a18a936f13d",
              "name": "BoredApeYachtClub",
              "symbol": "BAYC",
              "isERC721": true,
              "isERC1155": false,
              "supportsMetadata": true,
              "createdAt": "2024-10-03T15:23:35Z"
            }
          }
        }
        ```

        ### Common Use Cases

        1. **Token Standard Verification**
          * Check `isERC721`/`isERC1155` before token interactions.
          * Verify `supportsMetadata` for UI rendering.
        2. **Marketplace Listings**
          * Display contract name/symbol.
          * Show creation date for collection age.
        3. **Developer Tooling**
          * Validate contracts before integration.
          * Check for proxy patterns.
        4. **Analytics**
          * Track contract deployment trends.
          * Monitor metadata adoption rates.

        ### Best Practices

        * **Cache Responses**: Contract details rarely change.
        * **Error Handling**: Returns `null` for invalid addresses.
        * **Combination Query**: Pair with `tokens` for full collection data.

        ### Related Queries

        * `contracts`: For paginated lists of contracts.
        * `tokens`: To explore a contract's tokens.

        ### Query Format
      args:
        id: The contract address to query.
