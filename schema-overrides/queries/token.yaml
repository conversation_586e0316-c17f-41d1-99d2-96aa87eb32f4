Query:
  fields:
    token:
      description: |
        The `token` query retrieves detailed information about a specific NFT or token, including its metadata, ownership history, and blockchain state. This is the primary method to fetch individual token data from collections.

        ### Example

        #### Basic Token Fetch

        ```graphql
        query Token($contract: ID!, $tokenId: ID!) {
          token(contract: $contract, tokenID: $tokenId) {
            tokenID
            name
            description
            image {
              url
            }
            contract {
              name
              symbol
            }
            holders {
              balances {
                owner
              }
            }
          }
        }
        ```

        #### Variables

        ```json
        {
          "contract": "******************************************",
          "tokenId": "2341"
        }
        ```

        #### Response

        This response is an example only and may not reflect current data.

        ```json
        {
          "data": {
            "token": {
              "tokenID": "2341",
              "name": "Pudgy Penguin #2341",
              "description": "A collection 8888 Cute Chubby Pudgy Penquins sliding around on the freezing ETH blockchain.",
              "image": {
                "url": "QmNf1UsmdGaMbpatQ6toXSkzDpizaGmC9zfunCyoz1enD5/penguin/2341.png"
              },
              "contract": {
                "name": "PudgyPenguins",
                "symbol": "PPG"
              },
              "holders": {
                "balances": [
                  {
                    "owner": "******************************************"
                  }
                ]
              }
            }
          }
        }
        ```

        ### Common Use Cases

        1. **NFT Display**: Show token details and media in marketplaces or galleries.
        2. **Ownership Verification**: Confirm current holder of a specific token.
        3. **Metadata Analysis**: Inspect token attributes and media.
        4. **Historical Research**: Check creation/burn status.

        ### Best Practices

        * **Error Handling**: Returns `null` for non-existent tokens.
        * **Partial Data**: Some fields may be null if metadata is unavailable.
        * **Combination Query**: Pair with `contract` query for full context.

        ### Related Queries

        * `tokens`: For fetching multiple tokens from a collection.
        * `contract`: To verify token standards before querying.

        ### Query Format
      args:
        contract: Contract address where the token resides.
        tokenID: Unique token identifier.
