Query:
  fields:
    balances:
      description: |
        The `balances` query retrieves a paginated list of token/NFT holdings for user and contract addresses. It supports complex filtering across token standards and returns a `Balances!` object with cursor-based pagination.

        ### Example Query

        #### Basic Wallet Holdings in Descending Order

        ```graphql
        query GetWalletBalances($owner: ID!, $orderBy: BalanceOrderBy, $orderDirection: OrderDirection) {
          balances(owner: $owner, first: 20, orderBy: $orderBy, orderDirection: $orderDirection) {
            balances {
              value
              contract {
                name
                type
                id
              }
            }
            count
          }
        }
        ```

        #### Variables

        ```json
        {
          "owner": "******************************************",
          "orderBy": "VALUE",
          "orderDirection": "DESC"
        }
        ```

        #### Response

        This response is an example only and may not reflect current data.

        ```json
        {
          "data": {
            "balances": {
              "balances": [
                {
                  "value": "1",
                  "contract": {
                    "name": null,
                    "type": "ERC721",
                    "id": "******************************************"
                  }
                },
                {
                  "value": "1",
                  "contract": {
                    "name": "OpenEthereumToken",
                    "type": "ERC721",
                    "id": "******************************************"
                  }
                }
              ],
              "count": 2
            }
          }
        }
        ```

        ### Common Use Cases

        1. Wallet dashboards showing all token holdings.
        2. Bulk export of a collection's ownership data.
        3. Access control based on token quantities.
        4. Analytics on token distribution.

        ### Best Practices

        * Prefer `after` over `skip` for large datasets.
        * Combine `where` and `whereContract` for precise filtering.
        * Always check `count` before fetching all results.
        * Request only needed fields to improve performance.

        ### Related Queries

        * `balance`: For single balance lookups.
        * `token`: To explore token metadata separately.

        ### Query Format
      args:
        first: Default value is 10. Maximum items per page.
        skip: Default value is 0. Offset for legacy pagination.
        after: Cursor for next page (from previous response).
        owner: Required wallet/contract address to query.
        where: Filter by balance-specific fields (`tokenID`, `contract`).
        orderBy: "`TYPE`, `CONTRACT`, `TOKEN_ID`, `ACCOUNT`, `VALUE`, `BLOCK`,
          `CONTRACT_TOKEN_ID`, `CONTRACT_ACCOUNT`, `CONTRACT_TOKEN_ID_ACCOUNT`"
        orderDirection: "`ASC`, `DESC`"
        whereToken: Filter by token properties (`tokenID`, `name`, etc.).
        whereContract: Filter by contract properties (`type`, `name`, etc.).
