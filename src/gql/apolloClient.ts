/* eslint-disable no-undef */
import { ApolloClient, InMemoryCache, createHttpLink, DefaultOptions } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import https from "https";
import isPreview from "@/constants/isPreview";
import { getAuthToken } from "@/utils/getAuthToken";

const token = getAuthToken();

const agent = new https.Agent({ rejectUnauthorized: false });

const defaultOptions: DefaultOptions = {
	watchQuery: {
		fetchPolicy: isPreview ? "no-cache" : "cache-first",
		errorPolicy: "ignore",
	},
	query: {
		fetchPolicy: isPreview ? "no-cache" : "cache-first",
		errorPolicy: "all",
	},
};

const httpLink = createHttpLink({
	uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL,
	fetchOptions: {
		agent,
	},
});



const authLink = setContext(async (_, { headers }) => {
	return {
		headers: {
			...headers,
			authorization: token ? `Bearer ${token}` : "",
		}
	};
});

const cache = new InMemoryCache({
	typePolicies: {
		OneSourcePost: {
			fields: {
				seo: {
					merge(existing = {}, incoming) {
						return { ...existing, ...incoming };
					},
				},
			},
		},
		Query: {
			fields: {
				oneSourceOptionsPage: {
					merge(existing = {}, incoming) {
						return { ...existing, ...incoming };
					},
				},
			},
		},
	},
});

const client = new ApolloClient({
	link: authLink.concat(httpLink),
	cache: cache,
	defaultOptions: defaultOptions,
});

export default client;