import { gql } from "@apollo/client";
import {
	SeoOneSourcePageFragment,
	PostFieldsOnPostFragment,
	OneSourcePageFragment,
	OneSourcePostSEOOnFragment,
} from "./fragments";

export const GET_BLOG_POST = gql`
  ${PostFieldsOnPostFragment}
  query PostBySlug($id: ID!, $idType: OneSourcePostIdType!) {
    oneSourcePost(id: $id, idType: $idType) {
      id
      ...PostFields
      blocks(htmlContent: true, originalContent: true)
      content
    }
  }
`;

export const GET_BLOG_POST_DRAFT = gql`
  ${PostFieldsOnPostFragment}
  query DraftBySlug($id: ID!, $idType: OneSourcePostIdType!) {
    oneSourcePost(id: $id, idType: $idType, asPreview: true) {
      id
      ...PostFields
      blocks(htmlContent: true, originalContent: true)
      content
    }
  }
`;

export const GET_ALL_POST_SLUGS = gql`
  query AllPostSlugs {
    oneSourcePosts{
      edges {
        node {
          uri
          slug
        }
      }
    }
  }
`;

export const GET_ALL_ONESOURCE_POSTS = gql`
  ${PostFieldsOnPostFragment}
  query AllOneSourcePosts(
    $first: Int
    $after: String
    $field: PostObjectsConnectionOrderbyEnum!
    $order: OrderEnum!
    $search: String
  ) {
    oneSourcePosts(
      first: $first
      after: $after
      where: { orderby: { field: $field, order: $order }, search: $search }
    ) {
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
      edges {
        node {
          ...PostFields
        }
      }
    }
  }
`;

export const GET_ALL_ONESOURCE_POSTS_DRAFTS = gql`
${PostFieldsOnPostFragment}
query AllOneSourcePostsDrafts(
  $first: Int
  $after: String
  $field: PostObjectsConnectionOrderbyEnum!
  $order: OrderEnum!
) {
  oneSourcePosts(
    first: $first
    after: $after
    where: { status: DRAFT, orderby: { field: $field, order: $order } }
  ) {
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    edges {
      node {
        ...PostFields
      }
    }
  }
}
`;

export const GET_ALL_ONESOURCE_POSTS_BY_TAG = gql`
  ${PostFieldsOnPostFragment}
  query AllOneSourcePostsByTag(
    $id: ID!
    $first: Int
    $after: String
    $field: PostObjectsConnectionOrderbyEnum!
    $order: OrderEnum!
    $search: String
  ) {
    oneSourceTags(id: $id, idType: SLUG) {
      oneSourcePosts(
        first: $first
        after: $after
        where: { orderby: { field: $field, order: $order }, search: $search }
      ) {
        pageInfo {
          endCursor
          hasNextPage
          hasPreviousPage
          startCursor
        }
        edges {
          node {
            ...PostFields
          }
        }
      }
    }
  }
`;




export const GET_FEATURED_ONESOURCE_POSTS = gql`
  ${PostFieldsOnPostFragment}
  query FeaturedOneSourcePosts($first: Int, $search: String) {
    oneSourcePosts(first: $first, where: { search: $search }) {
      edges {
        node {
          ...PostFields
        }
      }
    }
  }
`;

export const GET_MENU = gql`
  query GetMenu($id: ID!) {
    menu(id: $id, idType: NAME) {
      id
      name
      menuItems(first: 100, where: { parentDatabaseId: 0 }) {
        nodes {
          label
          url
          childItems {
            nodes {
              label
              url
              path
              target
            }
          }
        }
      }
    }
  }
`;

export const GET_MAIN_MENUS = gql`
  query GetMainMenus($id: ID!, $id2: ID!) {
    mainMenu: menu(id: $id, idType: NAME) {
      id
      name
      menuItems(first: 100) {
        nodes {
          id
          label
          url
          parentId
          target
          childItems {
            nodes {
              id
              label
              url
              description
              target
              customMenuFields {
                darkMenuIcon {
                  node {
                    altText
                    sourceUrl
                  }
                }
                lightMenuIcon {
                  node {
                    altText
                    sourceUrl
                  }
                }
                darkLottie {
                  node {
                    filePath
                  }
                }
                lightLottie {
                  node {
                    filePath
                  }
                }
              }
            }
          }
        }
      }
    }
    secondaryMenu: menu(id: $id2, idType: NAME) {
      id
      name
      menuItems(first: 100) {
        nodes {
          id
          label
          url
          parentId
        }
      }
    }
  }
`;

export const GET_ALL_PAGE_SLUGS = gql`
  query AllPageSlugs {
    oneSourcePages {
      edges {
        node {
          uri
          slug
        }
      }
    }
  }
`;

export const GET_ALL_PAGES = gql`
  query allPages {
    oneSourcePages {
      pageInfo {
        endCursor
        startCursor
      }
      nodes {
        blocks
        date
        id
        isContentNode
        isFrontPage
        isPostsPage
        link
        title
        uri
        status
      }
    }
  }
`;

export const GET_POST_SEO_BY_SLUG = gql`
  ${OneSourcePostSEOOnFragment}
  query postSeoBySlug($id: ID!, $idType: OneSourcePostIdType!) {
    oneSourcePost(id: $id, idType: $idType) {
      ...OneSourcePostSEOOnFragment
    }
  }
`;

export const GET_POST_DRAFT_SEO_BY_SLUG = gql`
  ${OneSourcePostSEOOnFragment}
  query postDraftSeoBySlug($id: ID!, $idType: OneSourcePostIdType!) {
    oneSourcePost(id: $id, idType: $idType, asPreview: true) {
      ...OneSourcePostSEOOnFragment
    }
  }
`;

export const GET_PAGE_SEO_BY_URI = gql`
  ${SeoOneSourcePageFragment}
  query pageSeoByURI($uri: String!) {
    oneSourcePageBy(uri: $uri) {
      ...SeoOneSourcePageFragment
    }
  }
`;

export const GET_ALL_TAGS = gql`
  query allTags {
    allOneSourceTags {
      nodes {
        databaseId
        count
        description
        name
        slug
        uri
        taxonomy {
          node {
            label
            name
            public
            hierarchical
            description
            showUi
            showInGraphql
          }
        }
        taxonomyName
      }
    }
  }
`;

export const GET_TAG_BY_ID = gql`
  ${PostFieldsOnPostFragment}
  query onSourceTags($id: ID!, $idType: OneSourceTagsIdType!) {
    oneSourceTags(id: $id, idType: $idType) {
      oneSourcePosts {
        pageInfo {
          endCursor
          hasNextPage
          hasPreviousPage
          startCursor
        }
        edges {
          node {
            ...PostFields
          }
        }
      }
      name
      slug
      uri
    }
  }
`;

// Get Pages
export const GET_PAGE_BY_URI = gql`
  ${OneSourcePageFragment}
  query getPageByURI($uri: ID!) {
    oneSourcePage(idType: URI, id: $uri) {
      ...OneSourcePageFragment
    }
  }
`;

// Get Pages
export const GET_PAGE_AND_DRAFTS_BY_URI = gql`
  ${OneSourcePageFragment}
  query getPageAndDraftsByURI($uri: ID!) {
    oneSourcePage(idType: URI, id: $uri, asPreview: true) {
      ...OneSourcePageFragment
    }
  }
`;

export const GET_HOMEPAGE = gql`
  ${OneSourcePageFragment}
  query getHomepage {
    oneSourceOptionsPage {
      oneSourceOptions {
        homepage {
          nodes {
            ... on OneSourcePage {
              ...OneSourcePageFragment
            }
          }
        }
      }
    }
  }
`;

export const GET_HOMEPAGE_SLUG = gql`
  query getHomepageSlug {
    oneSourceOptionsPage {
      oneSourceOptions {
        homepage {
          nodes {
            slug
          }
        }
      }
    }
  }
`;

export const GET_BLOG_HERO = gql`
  query getBlogPageHero {
    oneSourceOptionsPage {
      oneSourceOptions {
        blogPage {
          nodes {
            ... on OneSourcePage {
              blogPage {
                heroImage {
                  node {
                    mediaItemUrl
                    altText
                  }
                }
                desktopAnimation {
                  node {
                    filePath
                  }
                }
                mobileAnimation {
                  node {
                    filePath
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_BLOG_PAGE = gql`
  ${OneSourcePageFragment}
  ${PostFieldsOnPostFragment}
  query getBlogPage {
    oneSourceOptionsPage {
      oneSourceOptions {
        blogPage {
          nodes {
            ... on OneSourcePage {
              ...OneSourcePageFragment
              blogPage {
                hideTags {
                  nodes {
                    slug
                  }
                }
                pinnedPosts {
                  edges {
                    node {
                      ...PostFields
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_HOMEPAGE_SEO = gql`
  ${SeoOneSourcePageFragment}
  query getHomepageSeo {
    oneSourceOptionsPage {
      oneSourceOptions {
        homepage {
          nodes {
            ... on OneSourcePage {
              id
              ...SeoOneSourcePageFragment
              featuredImage {
                node {
                  sourceUrl
                  altText
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_BLOG_PAGE_SEO = gql`
  ${SeoOneSourcePageFragment}
  query getBlogPageSeo {
    oneSourceOptionsPage {
      oneSourceOptions {
        blogPage {
          nodes {
            ... on OneSourcePage {
              id
              ...SeoOneSourcePageFragment
              featuredImage {
                node {
                  altText
                  sourceUrl
                }
              }
            }
          }
        }
      }
    }
  }
`;

// footer options
export const GET_FOOTER_OPTIONS = gql`
  query getFooterOptions {
    oneSourceOptionsPage {
      oneSourceFooter {
        socialLinks {
          discord
          linkedin
          telegram
          x
        }
        legalLinks {
          termsLink {
            target
            title
            url
          }
          privacyLink {
            target
            title
            url
          }
        }
      }
    }
  }
`;

// pricing options
export const GET_PRICING_OPTIONS = gql`
  query getPricingOptions {
    oneSourceOptionsPage {
      pricingOptions {
        plans {
          callToAction {
            target
            title
            url
          }
          custom
          includes {
            planFeature
          }
          name
          price
          priceDescription
        }
      }
    }
  }
`;

export const GET_MEDIA_ITEM_BY_ID = gql`
  query mediaItem($id: ID!) {
    mediaItem(id: $id, idType: DATABASE_ID) {
      sourceUrl
    }
  }
`;

export const GET_LOTTIE_BY_ID = gql`
  query lottieAnimation($id: ID!) {
    mediaItem(id: $id, idType: DATABASE_ID) {
      filePath
    }
  }
`;
