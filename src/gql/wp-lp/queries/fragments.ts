import { gql } from "@apollo/client";

export const AuthorFieldsOnUserFragment = gql`
  fragment AuthorFields on User {
    name
    firstName
    lastName
    description
    avatar {
      url
    }
  }
`;

export const PostFieldsOnPostFragment = gql`
  ${AuthorFieldsOnUserFragment}
  fragment PostFields on OneSourcePost {
    title
    excerpt
    slug
    date
    status
    featuredImage {
      node {
        sourceUrl
      }
    }
    author {
      node {
        ...AuthorFields
      }
    }
    oneSourceTags {
      edges {
        node {
          name
          slug
          onesourceTagSettings {
            tagColor
          }
        }
      }
    }
    seo {
      readingTime
    }
    onesourcePostOptions {
      showAuthor
    }
    lastEditedBy {
      node {
        revisions {
          edges {
            node {
              date
            }
          }
        }
      }
    }
    blocks(htmlContent: true, originalContent: true)
    content
  }
`;

export const SeoOneSourcePageFragment = gql`
  fragment SeoOneSourcePageFragment on OneSourcePage {
    seo {
      breadcrumbs {
        text
        url
      }
      canonical
      focuskw
      fullHead
      metaDesc
      metaKeywords
      metaRobotsNofollow
      metaRobotsNoindex
      opengraphAuthor
      opengraphDescription
      opengraphImage {
        altText
        mediaItemUrl
      }
      opengraphPublishedTime
      opengraphTitle
      opengraphUrl
      readingTime
      title
      twitterDescription
      twitterTitle
      twitterImage {
        mediaItemUrl
      }
    }
  }
`;

export const OneSourcePageFragment = gql`
  fragment OneSourcePageFragment on OneSourcePage {
    blocks(htmlContent: true, attributes: true, originalContent: true)
    featuredImage {
      node {
        altText
        blocks(htmlContent: true, attributes: true, dynamicContent: true)
        caption
        description
        date
      }
    }
    isFrontPage
    isPostsPage
    id
    link
    slug
    uri
    title
    status
    content
    pageOptions {
      headerTheme
      includeHeaderAndFooterMenus
    }
  }
`;

export const OneSourcePostSEOOnFragment = gql`
  fragment OneSourcePostSEOOnFragment on OneSourcePost {
    uri
      title
      id
      featuredImage {
        node {
          sourceUrl
        }
      }
      seo {
        breadcrumbs {
          text
          url
        }
        canonical
        focuskw
        fullHead
        metaDesc
        metaKeywords
        metaRobotsNofollow
        metaRobotsNoindex
        opengraphAuthor
        opengraphDescription
        opengraphImage {
          altText
          mediaItemUrl
        }
        opengraphPublishedTime
        opengraphTitle
        opengraphUrl
        title
        twitterDescription
        twitterTitle
        twitterImage {
          mediaItemUrl
        }
      }
  }
`;
