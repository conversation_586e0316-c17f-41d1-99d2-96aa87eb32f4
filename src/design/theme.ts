// @ts-nocheck
"use client";
import {
	theme as base,
	ColorModeScriptProps,
	extendTheme,
} from "@chakra-ui/react";
import { headerStyles } from "../components/Molecules/Header.tsx";
import { quoteStyles } from "../components/GutenbergBlocks/Quote.tsx";
import { oneSourceParagraphStyles } from "../components/GutenbergBlocks/Paragraph.tsx";
import { pullQuoteStyles } from "@/components/GutenbergBlocks/PullQuote.tsx";
import { columnBlockStyles } from "@/components/DesignBlocks/ColumnBlock.tsx";
import { groupBlockStyles } from "@/components/DesignBlocks/GroupBlock.tsx";
import { separatorBlockStyles } from "@/components/DesignBlocks/SeparatorBlock.tsx";
import { spacerBlockStyles } from "@/components/DesignBlocks/SpacerBlock.tsx";
import { detailsStyles } from "@/components/GutenbergBlocks/Details.tsx";
import { listStyles } from "@/components/GutenbergBlocks/List.tsx";
import { preformattedStyles } from "@/components/GutenbergBlocks/Preformatted.tsx";
import { tableStyles } from "@/components/GutenbergBlocks/Table.tsx";
import { embedStyles } from "../components/Embeds/EmbedBlock.tsx";
import { fileStyles } from "@/components/MediaBlocks/FileBlock.tsx";
import { galleryStyles } from "@/components/MediaBlocks/GalleryBlock.tsx";
import { imageStyles } from "@/components/MediaBlocks/ImageBlock/index.tsx";
import { mediaTextStyles } from "@/components/MediaBlocks/MediaTextBlock.tsx";
import { tagsStyles } from "@/components/Molecules/Tags.tsx";
import { coverImageStyles } from "@/components/Molecules/CoverImage.tsx";
import { postHeaderStyles } from "@/components/Molecules/PostHeader.tsx";
import { dateTextStyles } from "@/components/Molecules/Date.tsx";
import { footerStyles } from "@/components/Molecules/Footer/index.tsx";
import { introStyles } from "@/components/Molecules/Intro.tsx";
import { moreStoriesStyles } from "@/components/Molecules/MoreStories.tsx";
import { postBodyStyles } from "@/components/Molecules/PostBody.tsx";
import { postPreviewStyles } from "@/components/Molecules/PostPreview.tsx";
import { postTitleStyles } from "@/components/Molecules/PostTitle.tsx";
import { sectionSeparatorStyles } from "@/components/Molecules/SectionSeparator.tsx";
import { ContentBlockStyles } from "@/components/Molecules/Content.tsx";
import ButtonStyles from "./atoms/buttons.ts";
import { LinkStyles } from "./atoms/links.ts";
import { InputStyles } from "./atoms/inputs.ts";
import { SelectStyles } from "./atoms/select.ts";
import { CheckboxStyles } from "./atoms/checkbox.ts";
import oneSourceTextStyles from "@/design/atoms/textAtoms";
import TooltipStyles from "./atoms/tooltips.ts";
import "@/design/fonts.css";
import { layerStyles } from "./atoms/layerStyles";

// Text styles
const fonts = {
	heading: "\"Mabry Medium Pro\", sans-serif",
	body: "\"Figtree\", ui-sans-serif, system-ui, sans-serif",
	mono: "\"MonaspaceNeon\", monospace",
};
// Space
const space = {
	// Semantic Keys
	"2xs": "2px",
	xs: "4px",
	sm: "8px",
	md: "16px",
	lg: "32px",
	xl: "48px",
	"2xl": "64px",
	"3xl": "80px",
	"4xl": "96px",
	// Base Keys
	0.5: "0.5px",
	0: "0px",
	1: "4px",
	2: "8px",
	3: "12px",
	4: "16px",
	5: "20px",
	6: "24px",
	8: "32px",
	10: "40px",
	12: "48px",
	13: "56px",
	16: "64px",
	20: "80px",
	24: "96px",
	28: "120px",
	30: "132px",
	32: "140px",
	40: "160px",
	48: "180px",
	56: "200px",
	64: "220px",
	72: "240px",
	80: "260px",
};

// Colors
const colors = {
	// Pony Colors
	//greens
	"dataGreen.10": "rgba(25, 223, 174, 0.1)",
	"dataGreen.15": "rgba(25, 223, 174, 0.15)",
	"dataGreen.25": "rgba(25, 223, 174, 0.25)",
	"dataGreen.30": "rgba(25, 223, 174, 0.3)",
	dataGreenLight: "#E8FAF5",
	dataGreenBright: "#5FF6CC",
	dataGreen: "#19DFAE",
	midDataGreen: "#2AD19F",
	blockstream: { 100: "#071F21", 60: "#00272C" },
	darkGreen: "#071F21",
	midnightGreen: "#003235",
	midnightGreenLight: "#03514E",
	nightGreen: "#034C51",
	deepGreen: "#00272C",
	"midnightGreen.50": "rgba(0, 50, 53, 0.5)",
	"darkGreen.25": "rgba(7, 31, 33, 0.25)",
	"darkGreen.30": "rgba(7, 31, 33, 0.3)",
	"darkGreen.50": "rgba(7, 31, 33, 0.5)",
	"darkGreen.60": "rgba(7, 31, 33, 0.6)",
	"darkGreen.90": "rgba(7, 31, 33, 0.9)",

	// Transparent Colors in Solid Form

	// DataGreen.25 on DarkGreen Background
	"dataGreen25OnDarkGreen": "#0b4f45",

	// whites
	white: "#FFFFFF",
	syncLight: "#F9F5F1",
	whiteNode: "#FFFFFF",
	cloudSync: "#F2EAE2",
	desertSand: "#F9F5F1",
	"desertSand.25": "rgba(249, 245, 241, 0.25)",
	"desertSand.30": "rgba(249, 245, 241, 0.30)",
	"desertSand.80": "rgba(249, 245, 241, 0.8)",
	"desertSand.50": "rgba(249, 245, 241, 0.5)",
	ivoryDust: "#ECE0D4",
	almondCream: "#F2EAE2",
	toastedOat: "#E5D3C3",
	"toastedOat.35": "rgba(229, 211, 195, 0.35)",

	nodePulse: "#D1A8D5",
	deepLedger: "#361A3D",
	hashCore: "#311D19",
	beaconSignal: "#EF992F",
	browserUiDarkBg: "#3D3D3D",
	blueGray: "#8CC4F2",

	// browns
	roseBrown: "#E59B7B",
	darkBrown: "#311D19",
	"darkBrown.30": "rgba(49, 29, 25, 0.3)",
	"darkBrown.80": "rgba(49, 29, 25, 0.8)",
	"darkBrown.4": "rgba(49, 29, 25, 0.04)",

	// yellows
	yellowGold: "#EBC800",
	goldenSunset: "#EF992F",

	//tertiary colors
	lightOrchid: "#D1A8D5",
	orchid: "#C678DD",
	purple: "#B84DFF",
	darkOrchid: "#BD6AC5",
	darkPurple: "#482251",
	darkerPurple: "#361A3D",
	blue: "#19BADF",
	darkBlue: "#185F99",
	denimBlue: "#8CC4F2",
	teal: "#19DFAE",
	lime: "#9DDF19",
	lemonYellow: "#F6E36D",
	yellow: "#DFC419",
	orange: "#E59B7B",
	persimmon: "#C63131",
	burgundy: "#350001",
	darkBurgundy: "#2D1901",

	//linear gradients
	linearGradient: {
		toastedOat: "linear-gradient(0deg, #E5D3C3 0%, #E5D3C3 100%)",
		blockstream: "linear-gradient(30deg, #071F21 30%, #071F21 100%)",
		black: "linear-gradient(30deg, #000000 100%)",
		tealBlue: "linear-gradient(30deg, #092A2C 100%)",
		aquaBlue: "linear-gradient(30deg, #19BDBF 25%, #19DFAE 30%)",
		limeGreen: "linear-gradient(30deg, #9DDF19 100%)",
		beaconSignal: "linear-gradient(10deg, #EF992F 100%)",
		deepBlue: "linear-gradient(30deg, #00252B 100%)",
		navyBlue: "linear-gradient(30deg, #03323A 100%)",
		midnightBlue: "linear-gradient(30deg, #04404A 100%)",
		forestGreen: "linear-gradient(30deg, #0A2F31 100%)",
		brownDark: "linear-gradient(30deg, #2D1901 100%)",
		deepLedger: "linear-gradient(30deg, #361A3D 100%)",
		nodePulse: "linear-gradient(30deg, #D1A8D5 100%)",
		grayDark: "linear-gradient(30deg, #9B9BA1 100%)",
		lavender: "linear-gradient(25deg, #BD6AC5 100%)",
		plum: "linear-gradient(25deg, #D1A8D5 100%)",
		rosePink: "linear-gradient(25deg, #E08185 100%)",
		beaconSignal25: "linear-gradient(25deg, #EF992F 100%)",
		yellowGreen: "linear-gradient(25deg, #F6E36D 100%)",
		syncLight: "linear-gradient(25deg, #F9F5F1 100%)",
	},
};

const borders = {
	none: "none",
	thin: "1px solid",
	medium: "1.5px solid",
	thick: "2px solid",
	extraThick: "4.2px solid",
};

const oneSourceTheme = extendTheme({
	base,
	config: {
		cssVarPrefix: "oneSource",
		initialColorMode: "dark" as ColorModeScriptProps["initialColorMode"],
		useSystemColorMode: false,
	},
	colors,
	borders,
	fonts,
	textStyles: oneSourceTextStyles,
	space,
	radii: space,
	layerStyles: layerStyles,
	components: {
		Quote: quoteStyles,
		Header: headerStyles,
		Paragraph: oneSourceParagraphStyles,
		PullQuote: pullQuoteStyles,
		Column: columnBlockStyles,
		Group: groupBlockStyles,
		Separator: separatorBlockStyles,
		Spacer: spacerBlockStyles,
		Details: detailsStyles,
		List: listStyles,
		Preformatted: preformattedStyles,
		Table: tableStyles,
		Embed: embedStyles,
		File: fileStyles,
		Gallery: galleryStyles,
		Image: imageStyles,
		MediaText: mediaTextStyles,
		Tags: tagsStyles,
		CoverImage: coverImageStyles,
		PostHeader: postHeaderStyles,
		DateText: dateTextStyles,
		Footer: footerStyles,
		Intro: introStyles,
		MoreStories: moreStoriesStyles,
		PostBody: postBodyStyles,
		PostPreview: postPreviewStyles,
		PostTitle: postTitleStyles,
		SectionSeparator: sectionSeparatorStyles,
		Content: ContentBlockStyles,
		Button: ButtonStyles,
		Link: LinkStyles,
		Input: InputStyles,
		Select: SelectStyles,
		Checkbox: CheckboxStyles,
		Tooltip: TooltipStyles,
		Menu: {
			baseStyle: {
				list: {
					zIndex: "tooltip",
				},
			},
		},
	},
	fontSizes: {
		"2xs": "10px",
		xs: "12px",
		sm: "14px",
		md: "16px",
		lg: "20px",
		xl: "37px",
		"2xl": "48px",
		"3xl": "56px",
		"4xl": "60px",
		"5xl": "76px",
		"6xl": "81px",
		"7xl": "120px",
	},
	
	breakpoints: {
		xs:  "25em", // 400
		sm: "30em", // 480
		sm2: "40em", // 640
		md: "48em", // 768
		md2: "853px", // 853
		lg: "62em", // 992
		xl: "80em", // 1280
		"2xl": "96em", // 1536
	},

	sizes: {
		container: {
			"3xs": "106px",
			"2xs": "275px", // changed from 360px
			xs: "320px",
			sm: "480px",
			md: "660px",
			lg: "1002px",
			xl: "1200px",
			"2xl": "1344px",
			"3xl": "1440px",
		},
	},
	styles: {
		global: () => ({
			body: {
				bg: "darkGreen",
				color: "white",
				...oneSourceTextStyles.body1Medium,
			},
			"@keyframes pulsate-green": {
				"0%": { filter: "drop-shadow(0 0 3px rgba(221,250,120, 0))" },
				"40%": { filter: "drop-shadow(0 0 3px rgba(221,250,120, 1))" },
				"60%": { filter: "drop-shadow(0 0 3px rgba(221,250,120, 1))" },
				"100%": { filter: "drop-shadow(0 0 3px rgba(221,250,120, 0))" },
			  },
		}),
	},
});

export default oneSourceTheme;