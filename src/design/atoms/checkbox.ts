export const CheckboxStyles = {
	parts: ["control", "label", "icon"],
	baseStyle: {
		control: {
			borderRadius: 1,
			mt: 1,
			border: "2px solid",
			borderColor: "darkBrown.30",
			bg: "transparent",
			_checked: {
				bg: "toastedOat",
				borderColor: "toastedOat",				
				color: "nightGreen"
			},
			_disabled: {
				bg: "gray.100",
				borderColor: "gray.200",
			},
		},
		label: {
			color: "darkBrown.80",
			fontSize: "12px",
			lineHeight: "120%",
			fontWeight: "300"
		},
	},
	variants: {
		contactDark: {
			control: {
				bg: "transparent",
				borderColor: "dataGreen.30",
				_checked: {
					bg: "dataGreen",
					border: "0px",			
					color: "nightGreen"
				},
			},
			label: {
				color: "dataGreenLight",
			},
		},
	},
};
