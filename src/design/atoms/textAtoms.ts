
const titleStyles = {
	fontFamily: "heading",
	fontStyle: "normal",
	fontWeight: "500",
};
const bodyStyles = {
	fontFamily: "body",
	fontStyle: "normal",
};
const monoStyles = {
	fontFamily: "mono",
	fontStyle: "normal",
	textTransform: "uppercase",
};

const oneSourceTextStyles = {
	// category.level
	"headline1": {
		...titleStyles,
		fontSize: { base: "26px", md: "36px" },
		lineHeight: "100%",
	},
	"headline2": {
		...titleStyles,
		fontSize: { base: "24px", md: "30px" },
		lineHeight: "110%",
	},
	"headline3": {
		...titleStyles,
		fontSize: { base: "20px", md: "24px" },
		lineHeight: "110%",
	},
	"titleLg": {
		...titleStyles,
		fontSize: { base: "48px", md: "96px" },
		letterSpacing: { base: "-1.44px", md: "-2.28px" },
		lineHeight: "95%",
	},
	"titleXl": {
		...titleStyles,
		fontSize: { base: "81px" },
		letterSpacing: { base: "-3.24px" },
		lineHeight: "95%",
	},
	"titleBig": {
		...titleStyles,
		fontSize: { base: "60px", md: "120px" },
		letterSpacing: { base: "-1.8px", md: "-3.6px" },
		lineHeight: { base: "100%", md: "95%" },
	},
	"title1": {
		...titleStyles,
		fontSize: { base: "48px", md: "76px" },
		letterSpacing: { base: "-1.44px", md: "-2.28px" },
		lineHeight: "100%",
	},
	"title2": {
		...titleStyles,
		fontSize: { base: "42px", md: "76px" },
		letterSpacing: { base: "-1.26px", md: "-2.28px" },
		lineHeight: "105%",
	},
	"title3": {
		...titleStyles,
		fontSize: { base: "36px", md: "64px" },
		letterSpacing: { base: "-1.08px", md: "-1.92px" },
		lineHeight: "100%",
	},
	"title4": {
		...titleStyles,
		fontSize: { base: "30px", md: "48px" },
		letterSpacing: { base: "-0.9px", md: "-1.44px" },
		lineHeight: "100%",
	},
	"bodyXs": {
		...bodyStyles,
		fontWeight: "500",
		fontSize: { base: "12px", md: "14px" },
		lineHeight: { base: "150%", md: "120%" },
	},
	"body1Medium": {
		...bodyStyles,
		fontWeight: "500",
		fontSize: { base: "19px", md: "20px" },
		lineHeight: "120%",
	},
	"body1Semibold": {
		...bodyStyles,
		fontWeight: "600",
		fontSize: { base: "19px", md: "20px" },
		lineHeight: "120%",
	},
	"body2Medium": {
		...bodyStyles,
		fontWeight: "500",
		fontSize: { base: "17px" },
		lineHeight: { base: "130%" },
	},
	"body2Semibold": {
		...bodyStyles,
		fontWeight: "600",
		fontSize: { base: "17px" },
		lineHeight: { base: "130%" },
	},
	"body3Medium": {
		...bodyStyles,
		fontWeight: "500",
		fontSize: { base: "15px" },
		lineHeight: { base: "120%" },
		letterSpacing: "0.15px",
	},
	"body3Semibold": {
		...bodyStyles,
		fontWeight: "600",
		fontSize: { base: "15px" },
		lineHeight: { base: "150%" },
	},
	"body3Thin" : {
		...bodyStyles,
		fontWeight: 300,
		fontSize: {base: "15px", md: "17px"},
		lineHeight: "150%",
	},
	"mono1Medium": {
		...monoStyles,
		fontSize: "12px",
		fontWeight: "500",
		lineHeight: "120%",
		letterSpacing: { base: "0.24px", md: "0.36px" },

	},
	"mono1Semibold": {
		...monoStyles,
		fontSize: "12px",
		fontWeight: "600",
		lineHeight: "120%",
		letterSpacing: { base: "0.4px" },

	},
	"mono2Medium": {
		...monoStyles,
		fontSize: "14px",
		fontWeight: "500",
		lineHeight: "100%",
		letterSpacing: "0.28px",
		textEdge: "cap",
		leadingTrim: "both",

	},
	"mono2Semibold": {
		...monoStyles,
		fontSize: "14px",
		fontWeight: "500",
		lineHeight: "100%",
		letterSpacing: "0.28px",

	},
	"mono3Medium": {
		...monoStyles,
		fontSize: "16px",
		fontWeight: "500",
		lineHeight: "120%",
		letterSpacing: "0.32px",

	},
	"mono3Semibold": {
		...monoStyles,
		fontSize: "16px",
		fontWeight: "600",
		lineHeight: "120%",
		letterSpacing: "0.32px",

	},
};

export default oneSourceTextStyles;
