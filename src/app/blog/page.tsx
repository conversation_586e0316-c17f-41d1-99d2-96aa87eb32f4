import client from "@/gql/apolloClient";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { TaxonomySeo } from "@/types/generated";
import { GET_BLOG_PAGE, GET_BLOG_PAGE_SEO } from "@/gql/wp-lp/queries";
import generatePageMetadata from "@/utils/generatePageMetadata";
import PageWrapper from "@/components/PageWrapper";
import Loading from "@/app/loading";
import BlogHomeContainer from "@/components/BlogHome/BlogHomeContainer";
import BlogIndex from "@/components/BlogIndex";
import isPreview from "@/constants/isPreview";

export const revalidate = isPreview ? 5 : 300;
export const dynamic = isPreview ? "force-dynamic" : "auto";

export async function generateMetadata(): Promise<Metadata> {
	const { data } = await client.query({
		query: GET_BLOG_PAGE_SEO,
	});
	const { seo } = data.oneSourceOptionsPage.oneSourceOptions.blogPage.nodes[0];

	const { featuredImage } = data.oneSourceOptionsPage.oneSourceOptions.blogPage.nodes[0];

	const metadata = generatePageMetadata({ seo: seo as TaxonomySeo, fallbackImage: featuredImage.node.sourceUrl });

	return metadata as Metadata;
}


export default async function BlogPage() {
	const { loading, error, data } = await client.query({
		query: GET_BLOG_PAGE,
	});
	if (error) throw error;
	if (loading) return <Loading />;
	if (!data) return notFound();

	const { pinnedPosts, hideTags } = data.oneSourceOptionsPage.oneSourceOptions.blogPage.nodes[0].blogPage;

	const blogPageNodes = data.oneSourceOptionsPage.oneSourceOptions.blogPage.nodes[0];
	const { blocks, pageOptions: { headerTheme, includeHeaderAndFooterMenus } } = blogPageNodes;

	if (!pinnedPosts || !Array.isArray(pinnedPosts.edges)) {
		console.error("pinnedPosts.edges is not a valid array:", pinnedPosts);
		return null;
	}
	
	const pinnedPostSlugs = pinnedPosts.edges.map((edge: { node: { slug?: string } }) => {
		if (edge.node && edge.node.slug) {
			return edge.node.slug;
		} else {
			console.warn("Missing slug in edge:", edge);
			return null;
		}
	}).filter(Boolean);

	const hideTagSlugs = hideTags?.nodes?.map((node: { slug: string }) => node.slug) || [];

	return (
		<PageWrapper
			headerTheme={headerTheme}
			includeHeaderAndFooterMenus={includeHeaderAndFooterMenus}
		>
			<BlogHomeContainer
				blocks={blocks}
				hideFeaturedPosts={false}
				excludeTags={hideTagSlugs}
				pinnedPosts={pinnedPosts.edges.map((edge: { node: { slug?: string } }) => edge.node)}
			>
				
				<BlogIndex
					numOfResults={9}
					showSort={false}
					mobileCarousel={false}
					excludePosts={pinnedPostSlugs}
					excludeTags={hideTagSlugs}
				/>
			</BlogHomeContainer>
		</PageWrapper>
	);
}