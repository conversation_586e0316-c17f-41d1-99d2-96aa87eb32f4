import PostBody from "@/components/Molecules/PostBody";
import PostHeader from "@/components/Molecules/PostHeader";
import { Box, Container } from "@chakra-ui/react";
import CoverImage from "@/components/Molecules/CoverImage";
import client from "@/gql/apolloClient";
import { Metadata } from "next";
import {
	GET_ALL_POST_SLUGS,
	GET_BLOG_POST,
	GET_BLOG_POST_DRAFT,
	GET_POST_DRAFT_SEO_BY_SLUG,
	GET_POST_SEO_BY_SLUG,
} from "@/gql/wp-lp/queries";
import { OneSourcePost, OneSourceTagsToOneSourcePostConnectionEdge, TaxonomySeo } from "@/types/generated";
import generatePageMetadata from "@/utils/generatePageMetadata";
import { notFound } from "next/navigation";
import Loading from "@/app/loading";
import PageWrapper from "@/components/PageWrapper";
import isPreview from "@/constants/isPreview";
import LastEdited from "@/components/Molecules/LastEdited";

export const revalidate = isPreview ? 5 : 300;
export const dynamic = isPreview ? "force-dynamic" : "auto";

interface PostProps {
  params: {
    slug: string
  }
}

export async function generateStaticParams() {
	const { data } = await client.query({
		query: GET_ALL_POST_SLUGS,
	});

	return data.oneSourcePosts.edges.map(
		({ node }: { node: { slug: string } }) => ({
			slug: node.slug,
		})
	);
}

export async function generateMetadata({
	params,
}: PostProps): Promise<Metadata> {
	const { data } = await client.query({
		query: isPreview ? GET_POST_DRAFT_SEO_BY_SLUG : GET_POST_SEO_BY_SLUG,
		variables: {
			id: params.slug,
			idType: "SLUG",
		},
	});

	const post = data?.oneSourcePost;

	const metadata = generatePageMetadata({seo: post?.seo as TaxonomySeo, page: post as OneSourcePost});

	return metadata as Metadata;
}

export default async function PostPage({ params: { slug } }: PostProps) {

	const {
		data,
		error,
		loading,
	} = await client.query({
		query: isPreview ? GET_BLOG_POST_DRAFT : GET_BLOG_POST,
		variables: {
			id: slug,
			idType: "SLUG",
		},
	});

	if (error || !data?.oneSourcePost) {
		return notFound();
	}

	if (loading) {
		return <Loading />;
	}

	
	const post = data.oneSourcePost;
	const { blocks } = data.oneSourcePost;
	const tags = post.oneSourceTags?.edges.map((edge: OneSourceTagsToOneSourcePostConnectionEdge) => edge.node as unknown) || [];
	
	const showAuthor = post.onesourcePostOptions?.showAuthor;
	const revisions = post.lastEditedBy?.node?.revisions?.edges;
	const lastEdited = revisions && revisions.length ? revisions[0]?.node?.date : null;

	return (
		<PageWrapper headerTheme={"light"} includeHeaderAndFooterMenus={true}>
			<Box
				minW={"100vw" }
				minH={"100vh"}
				position={"relative"}
				zIndex={0}
				p={0}
				background={"white"}
			>
				<Container
					display={"flex"}
					flexDirection={"column"}
					justifyContent={"center"}
					alignItems={"center"}
					px={{ base: 0, sm: 4, md: 16 }}
					pt={{ base: 24, md: 32 }}
				>
					<PostHeader
						title={post.title}
						date={post.date}
						readingTime={post.seo?.readingTime}
						tags={tags}
						status={post.status}
						showAuthor={showAuthor}
						author={post.author?.node}
					/>
					<Box mb={{ base: 2, md: 4 }} maxW={"container.lg"} >
						{post?.featuredImage?.node && (
							<CoverImage
								title={post.title}
								coverImage={post.featuredImage.node}
							/>
						)}
					</Box>
				</Container>
				<PostBody content={post.content} blocks={blocks} />
				<LastEdited lastEdited={lastEdited} />
			</Box>
		</PageWrapper>

	);
}
