import client from "@/gql/apolloClient";
import { GET_HOMEPAGE, GET_HOMEPAGE_SEO } from "@/gql/wp-lp/queries";
import { Metadata } from "next";
import Loading from "@/app/loading";
import BlockHandler from "@/components/BlockHandler";
import generatePageMetadata from "@/utils/generatePageMetadata";
import { TaxonomySeo } from "@/types/generated";
import PageWrapper from "@/components/PageWrapper";
import isPreview from "@/constants/isPreview";

export const revalidate = isPreview ? 5 : 300;
export const dynamic = isPreview ? "force-dynamic" : "auto";

export async function generateMetadata(): Promise<Metadata> {
	const { data } = await client.query({
		query: GET_HOMEPAGE_SEO,
	});
	const { seo } = data.oneSourceOptionsPage.oneSourceOptions.homepage.nodes[0];
	const { featuredImage } = data.oneSourceOptionsPage.oneSourceOptions.homepage.nodes[0];

	const metadata = generatePageMetadata({ seo: seo as TaxonomySeo, fallbackImage: featuredImage.node.sourceUrl });

	return metadata as Metadata;
}

export default async function Homepage() {
	const { loading, error, data } = await client.query({
		query: GET_HOMEPAGE,
	});

	if (error) throw error;
	if (loading) return <Loading />;
	if (!data) return null;

	const homepageNodes = data.oneSourceOptionsPage.oneSourceOptions.homepage.nodes[0];
	const { blocks, pageOptions: { headerTheme, includeHeaderAndFooterMenus } } = homepageNodes;

	return (
		<PageWrapper headerTheme={headerTheme} includeHeaderAndFooterMenus={includeHeaderAndFooterMenus}>
			<BlockHandler blocks={blocks} />
		</PageWrapper>
	);
}