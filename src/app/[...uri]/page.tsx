import client from "@/gql/apolloClient";
import BlockHandler from "@/components/BlockHandler";
import { Container, Text, Box } from "@chakra-ui/react";
import Loading from "@/app/loading";
import {
	GET_ALL_PAGE_SLUGS,
	GET_PAGE_AND_DRAFTS_BY_URI,
	GET_PAGE_BY_URI,
	GET_PAGE_SEO_BY_URI,
} from "@/gql/wp-lp/queries/index";
import { Metadata } from "next";
import generatePageMetadata from "@/utils/generatePageMetadata";
import { Page, TaxonomySeo } from "@/types/generated";
// import DynamicBackground from "@/components/LivingLibrary/DynamicBackground";
import PageWrapper from "@/components/PageWrapper";
import { notFound } from "next/navigation";
import { ContentNodeBlocksArgs } from "@/types";
import isPreview from "@/constants/isPreview";

export const revalidate = isPreview ? 5 : 300;
export const dynamic = isPreview ? "force-dynamic" : "auto";

interface PageProps {
  params: {
    uri: string[]
  }
}

export async function generateStaticParams() {
	const { data } = await client.query({
		query: GET_ALL_PAGE_SLUGS,
	});

	return data.oneSourcePages.edges.map(
		({ node }: { node: { uri: string | string[] } }) => {
			const uriArray = Array.isArray(node.uri) ? node.uri : node.uri.split("/");
			return {
				uri: uriArray.filter((segment) => segment),
			};
		}
	);
}

export async function generateMetadata({
	params,
}: PageProps): Promise<Metadata> {
	const uri = params.uri.join("/");

	const {
		data: { oneSourcePage },
		error,
	} = await client.query({
		query: GET_PAGE_SEO_BY_URI,
		variables: { uri: `/one-source-page/${uri}/` },
	});

	if (error) throw error;
	if (!oneSourcePage) {
		return {};
	}

	const metadata = generatePageMetadata({
		seo: oneSourcePage.seo as TaxonomySeo,
		page: oneSourcePage as Page,
		fallbackImage: oneSourcePage.featuredImage?.node?.sourceUrl
	});

	return metadata as Metadata;
}

export default async function PageTemplate({ params }: PageProps) {
	const uri = params.uri.join("/");

	const {
		loading,
		error,
		data: { oneSourcePage },
	} = await client.query({
		query: isPreview ? GET_PAGE_AND_DRAFTS_BY_URI : GET_PAGE_BY_URI,
		variables: { uri: `/one-source-page/${uri}/` },
	});

	if (error) throw error;
	if (loading) return <Loading />;
	if (!oneSourcePage) return notFound();

	const {
		pageOptions: { headerTheme, includeHeaderAndFooterMenus },
	} = oneSourcePage;

	const hasHero =
    oneSourcePage.blocks?.some((block: ContentNodeBlocksArgs) =>
    	block.name?.startsWith("acf/hero-") || block.name === "acf/connect-box" || block.name === "acf/pricing-section"
    ) ?? false;

	return (
		<PageWrapper
			headerTheme={headerTheme}
			includeHeaderAndFooterMenus={includeHeaderAndFooterMenus}
		>
			<Container maxW="100vw" p={0} bg="darkGreen">
				{!hasHero && (
					<Box w="100%" px={6}>
						<Container maxW="container.md" p={0} pt={48} pb={0}>
							<Text as="h1" textStyle="title1" w="100%" textAlign="center">
								{oneSourcePage.title}
							</Text>
						</Container>
					</Box>
				)}

				{oneSourcePage.blocks?.length > 0 && (
					<BlockHandler
						blocks={oneSourcePage.blocks}
						includeHeaderAndFooterMenus={includeHeaderAndFooterMenus}
					/>
				)}

				{/* <DynamicBackground
						width={600}
						height={600}
						excludeAreas={[
							{ x: 0, y: 0, width: 2, height: 2 }, // Reserve a 6x5 space at (0,0)
							// { x: 6, y: 1, width: 3, height: 2 }  // Reserve a 3x2 space at (6,1)
						]}
						// Excluded areas = [ {
						// 	x: number;  // Grid X position (col) / out of 10
						// 	y: number;  // Grid Y position (row) / out of 10
						// 	width: number; // Width in grid units / out of 10
						// 	height: number; // Height in grid units / out of 10
						// } ]
					/> */}
			</Container>
		</PageWrapper>
	);
}
