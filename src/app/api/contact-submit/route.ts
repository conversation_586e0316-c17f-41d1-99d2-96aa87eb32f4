import { NextResponse } from "next/server";

export async function POST(request: Request) {
	const { name, email, source, solution, building, company, telegram, discord, contactConsent } = await request.json();

	if (!email) {
		return NextResponse.json({ message: "Email is required" }, { status: 400 });
	}

	const apiUrl = `${process.env.PIPEDRIVE_PERSONS_API_URL}?api_token=${process.env.PIPEDRIVE_API_TOKEN}`;

	try {
		const response = await fetch(apiUrl, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				name: name,
				email: email,
				marketing_status: contactConsent ? "subscribed" : "none",
				"c4531b85d1c7b9e5f1b997868b59e44ee1d0e1d9": source, // form_source
				"895db902cb93eddd3340b27406cfdfdb9046ca20": solution ? solution : "", // Solution they're exploring
				"25165eb12cfca4c69d8c17986e40b35eacfcfa9a": building ? building : "", // What they're building
				"e29d3e1cd8fe13f134ec25ab6e7d529bae9fc160": company ? company : "",
				"99d8595571a83543c9168214a07804eea91bcb50": telegram ? telegram : "",
				"6d39876098c6346d07256a9138bc63a749757809": discord ? discord : "",
			}),
		});

		if (!response.ok) {
			throw new Error("Failed to save submission to Pipedrive");
		}

		return NextResponse.json(
			{ message: "Submission saved successfully" },
			{ status: 200 }
		);
	} catch (error) {
		console.error("Error saving submission:", error);
		return NextResponse.json(
			{ message: (error as Error).message },
			{ status: 500 }
		);
	}
}
