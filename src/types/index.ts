import { Input<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ala<PERSON> } from "./generated";

export type StringKeyedObject = {
	[key: string]: string;
};

export type GlobalStyleBorder = {
  width?: string | number;
  style?: string;
}

export type AttributeGlobalStyles = {
  [key: string]: string | number | null | undefined | GlobalStyleBorder;
}

export type BlockAttributes = {
  globalStyles?: AttributeGlobalStyles;
  content?: Maybe<Scalars["String"]["output"]>;
  height?: number;
  width?: number;
  data?: StringKeyedObject;
  url?: Maybe<Scalars["String"]["output"]>;
  linkDestination?: Maybe<Scalars["String"]["output"]>;
  verticalAlignment?: Maybe<Scalars["String"]["output"]>;
  mediaType? : Maybe<Scalars["String"]["output"]>;
  mediaLink?: Maybe<Scalars["String"]["output"]>;
  mediaPosition?: Maybe<Scalars["String"]["output"]>;
  href?: Maybe<Scalars["String"]["output"]>;
  level?: Maybe<Scalars["String"]["output"]>;
  innerBlocks?: ContentNodeBlocksArgs[];
  type?: Maybe<Scalars["String"]["output"]>;
  providerNameSlug?: Maybe<Scalars["String"]["output"]>;
  responsive?: Maybe<Scalars["Boolean"]["output"]>;
  style?: Maybe<Scalars["String"]["output"]>;
  ordered?: Maybe<Scalars["Boolean"]["output"]>;
  displayWidth?: Maybe<Scalars["String"]["output"]>;
  displayHeight?: Maybe<Scalars["String"]["output"]>;
}

/** Nodes used to manage content */
export type ContentNodeBlocksArgs = {
  attributes?: BlockAttributes;
  dynamicContent?: InputMaybe<Scalars["Boolean"]["input"]>;
  htmlContent?: InputMaybe<Scalars["Boolean"]["input"]>;
  originalContent?: InputMaybe<Scalars["Boolean"]["input"]>;
  postTemplate?: InputMaybe<Scalars["Boolean"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  innerBlocks?: ContentNodeBlocksArgs[];
};

export type Node = {
	childNodes: Node[];
	nodeType: number;
	rawText: string;
};

export type RepeaterGroupData = { [key: string]: string | number };

export interface IconType {
  type: string;
  value: string;
}

export interface CtaType {
  title: string;
  url: string;
  target: "_blank" | "_self" | "";
}

export interface PageOptions {
  headerTheme?: "light" | "dark";
  includeHeaderAndFooterMenus?: boolean;
}

export interface InputChangeEvent {
  target: {
    value: string
  }
}

type FormField = "telegram" | "discord" | "company" | "building" | "solution" | "source" | "email" | "name" | "contactConsent" | `contactConsent_${number}`;

export type FormFields = FormField[];