div.container {
    padding: 0px 1rem;
}

@media (min-width: 768px) {
    div.container {
        padding: 0px 40px;
    }
}

ul.theme-doc-sidebar-menu li.menu__list-item * {
    border-radius: 0px;
    font-weight: 300 !important;
    font-size: 14px;
}

li.menu__list-item:not(:first-child) {
    margin-top: 0;
}

ul.theme-doc-sidebar-menu.menu__list,
ul.theme-doc-sidebar-menu.menu__list > li.menu__list-item:not(.menu__list-item--collapsed) div.menu__list-item-collapsible ~ ul.menu__list li {
    border-left: 1px solid var(--ifm-color-subtle);
    margin-top: 0;
}

ul.theme-doc-sidebar-menu.menu__list > li.menu__list-item:not(.menu__list-item--collapsed) div.menu__list-item-collapsible.menu__list-item-collapsible--active > a,

ul.theme-doc-sidebar-menu.menu__list > li.menu__list-item:not(.menu__list-item--collapsed) div.menu__list-item-collapsible:not(.menu__list-item-collapsible--active) ~ ul.menu__list > *:not(.menu__list-item-collapsible) > a.menu__link--active  {
    border-left: 2px solid var(--ifm-toc-link-hover-color);
    color: var(--ifm-toc-link-hover-color);
}

div.menu__list-item-collapsible--active, .menu__link--active:not(.menu__link--sublist) {
    background: transparent;
}

button.menu__caret:before, a.menu__link--sublist-caret:after {
    width: 1rem;
    height: 1rem;
    background-size: contain;
}

a.theme-edit-this-page {
    display: none;
}

.anchor a {
    color: var(--ifm-color-primary-lightest);
}

a.menu__link, a.table-of-contents__link, a.pagination-nav__link, a.footer__link-item, a.navbar__link {
    text-decoration: none;
}

span.badge {
    font-weight: 400;
    background-color: var(--ifm-color-subtle);
    border: 0px;
    border-radius: 4px;
    color: var(--ifm-font-color-base);
}

button.DocSearch.DocSearch-Button {
    border-radius: 4px;
    font-family: var(--ifm-font-family-base);
    font-weight: 300;
    background: transparent;
    border: 1px solid var(--ifm-color-subtle);
}

button.DocSearch.DocSearch-Button:hover,
button.DocSearch.DocSearch-Button:focus,
button.DocSearch.DocSearch-Button:active {
    box-shadow: none;
    border-color: var(--ifm-color-primary-lightest);
}

input.DocSearch-Input {
    font-size: 1.1em;
}

form.DocSearch-Form {
    box-shadow: none;
    border-radius: 4px;
    border: 1px solid var(--ifm-search-border-subtle);
    height: 48px;
    background: var(--ifm-background-surface-color);
}

div.DocSearch-Modal {
    font-family: var(--ifm-font-family-base);
    background: var(--ifm-modal-background-color);
    box-shadow: inset 0px 0px 0 0 transparent, 0 3px 8px 0 rgba(0, 3, 9, 0.558);
}

footer.DocSearch-Footer {
    background: var(--ifm-modal-background-color);
    box-shadow: none;
    border-top: 1px solid var(--ifm-search-border-subtle);
}

.DocSearch-Input::placeholder {
    font-weight: 300;
    letter-spacing: -0.5px;
}

kbd.DocSearch-Commands-Key, kbd.DocSearch-Button-Key {
    background: transparent;
    box-shadow: none;
    border: 1px solid var(--ifm-color-subtle);
    padding: 0;
}

a.footer__link-item {
    color: var(--ifm-font-color-base);
    opacity: 0.6;
    font-weight: 300;
    transition: all .25s ease-in-out;
}

a.footer__link-item:hover {
    opacity: 1;
    text-decoration: none;
}

h2.anchor kbd {
    border: none;
    box-shadow: none;
    padding: 0;
    background-color: transparent;
    font-weight: 600;
}

h2.anchor code {
    border: none;
    box-shadow: none;
    letter-spacing: -1px;
    /* padding: 0; */
}

h2.anchor a {
    color: var(--ifm-font-color-base);
    text-decoration: none;
}

.anchor {
    position: relative;
}

.anchor a.hash-link {
    opacity: 0;
    padding-left: 0;
    color: var(--ifm-font-color-base);
    position: absolute;
    left: -26px;
    text-decoration: none;
}

.anchor a.hash-link:hover {
    text-decoration: none;
}

.anchor:hover a.hash-link {
    opacity: 1;
}

button.graphiql-un-styled[data-name="headers"] {
    display: none !important;
}

button.navbar__toggle.clean-btn svg path {
    stroke: var(--ifm-font-color-base);
}