:root {
  /* Body text */
  --ifm-font-family-base: 'Figtree', system-ui, sans-serif;
  
  /* Headings */
  --ifm-heading-font-family: var(--ifm-font-family-base);

  /* Accent font */
  --ifm-font-family-accent: 'Neon', var(--ifm-font-family-base);
  
  /* Code blocks */
  --ifm-font-family-monospace: 'Neon-Regular', monospace;

    /* Disable faux bolding */
    font-synthesis: none;

    --ifm-breadcrumb-border-radius: 4px;
}

/* ===== LIGHT THEME ===== */
:root {
  /* Light theme background */
  --ifm-background-color: #ffffff;
  --ifm-background-surface-color: #ffffff;
  
  /* Light theme text */
  --ifm-font-color-base: #311d19;
  
  /* Light theme colors */
  --ifm-link-color: #185f99;
  --ifm-link-hover-color: #6cbdff;
  --ifm-toc-link-hover-color: rgb(84, 144, 149); 
  --ifm-toc-link-hover-bg: rgb(235, 244, 245);
  --ifm-color-primary: #071f21;
  --ifm-color-primary-dark: #061c1e;
  --ifm-color-primary-darker: #061a1c;
  --ifm-color-primary-darkest: #051617;
  --ifm-color-primary-light: #082224;
  --ifm-color-primary-lighter: #082426;
  --ifm-color-primary-lightest: #09282b;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
  --ifm-color-subtle: rgba(49, 29, 25, 0.1);
  --ifm-border-width: 2px;
  --ifm-breadcrumb-item-background-active: var(--ifm-color-subtle);
  --ifm-search-border-subtle: #F2EAE2;
  --ifm-modal-background-color: #F9F5F1;
  --ifm-link-decoration: underline;
  --ifm-alert-border-color: #F2EAE2;

  .alert--info {
    --ifm-alert-border-color: #E5D3C3;
    --ifm-alert-background-color: #F9F5F1;
    --ifm-alert-foreground-color: rgba(49, 29, 25, 0.8);
  }

  div.theme-code-block, div.theme-code-block pre {
    background-color: #F9F5F1 !important;
    background: #F9F5F1 !important;
  }
}



/* ===== DARK THEME ===== */
:root[data-theme='dark'] {
  /* Dark theme background */
  --ifm-background-color: #071f21;
  --ifm-background-surface-color: #0a282b;
  --ifm-navbar-background-color: #071f21;
  
  /* Dark theme text */
  --ifm-font-color-base: #ffffff;

  /* Dark theme navbar link colors */
  --ifm-navbar-link-color: #ffffff;
  --ifm-toc-link-hover-color: #19dfae;
  --ifm-navbar-link-hover-color: #19dfae;
  --ifm-navbar-link-active-color: #19dfae;
  
  /* Dark theme colors */
  --ifm-link-color: #19dfae;
  --ifm-link-hover-color: #5FF6CC;
  --ifm-color-primary: #19dfae;
  --ifm-color-primary-dark: #17c99d;
  --ifm-color-primary-darker: #15be94;
  --ifm-color-primary-darkest: #119c7a;
  --ifm-color-primary-light: #2ae7b8;
  --ifm-color-primary-lighter: #35e8bc;
  --ifm-color-primary-lightest: #56ecc7;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
  --ifm-color-subtle: rgba(249, 249, 241, 0.1);
  --ifm-breadcrumb-item-background-active: var(--ifm-color-subtle);
  --ifm-search-border-subtle: rgba(25, 223, 174, 0.25);
  --ifm-modal-background-color: #071F21;
  .alert--info {
    --ifm-alert-border-color: rgba(249, 249, 241, 0.3);
    --ifm-alert-background-color: #0a282b;
    --ifm-alert-foreground-color: #ffffff;
  }
  div.theme-code-block, div.theme-code-block pre {
    background-color: #003235 !important;
    background: #003235 !important;
  }
}