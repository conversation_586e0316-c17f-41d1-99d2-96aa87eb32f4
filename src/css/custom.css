/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

@import './fonts.css';
@import './vars.css';
@import './overrides.css';
@import url('https://cdn.jsdelivr.net/npm/firacode@6.2.0/distr/fira_code.css');

.footer {
  background-color: var(--ifm-navbar-background-color);
  color: var(--ifm-font-color-base);
  border-top: 1px solid var(--ifm-color-subtle);
  font-weight: 300;
}

.footer__title {
  color: var(--ifm-font-color-base);
  font-family: var(--ifm-font-family-heading);
  font-size: 1.1em;
  font-weight: 600; /* Match your accent font weight */
  letter-spacing: 0.02em; /* Optional: if your accent font uses letter-spacing */
}

.footer__copyright {
  opacity: 0.6;
  font-size: 0.9em;
  font-weight: 300;
}

.pagination-nav__sublabel {
  font-family: var(--ifm-font-family-accent);
}

.navbar .navbar__items--right button[class*='toggle'] {
  color: var(--ifm-font-color-base);
}

.navbar .navbar__items--right button[class*='toggle'] svg {
  fill: currentColor;
}

/* Light theme hover */
:root:not([data-theme='dark']) .navbar .navbar__items--right button[class*='toggle']:hover {
  color: var(--ifm-font-color-base); /* Your light theme primary color */
  background: var(--ifm-color-subtle);
}

/* Dark theme hover */
:root[data-theme='dark'] .navbar .navbar__items--right button[class*='toggle']:hover {
  color: var(--ifm-font-color-base); /* Your dark theme primary color */
  background: var(--ifm-color-subtle);
}

/* ===== TOC Color Fixes ===== */
/* Light Theme */
:root:not([data-theme='dark']) {
  /* TOC hover state */
  .table-of-contents__link:hover {
    color: var(--ifm-link-hover-color); /* #071f21 */
    opacity: 1;
  }

  /* TOC active state (matches sidebar) */
  .table-of-contents__link--active {
    color: var(--ifm-color-primary-lightest);
    border-left-color: var(--ifm-color-primary-lightest);
  }

  /* Sidebar active state (keep your existing) */
  .menu__link--active {
    color: var(--ifm-menu-color);
    /* border-left: var(--ifm-border-width) solid var(--ifm-toc-link-hover-color); */
  }
}

/* Dark Theme (unchanged) */
:root[data-theme='dark'] {
  .table-of-contents__link:hover {
    color: var(--ifm-color-primary); /* #19dfae */
  }
  .table-of-contents__link--active {
    color: var(--ifm-color-primary);
  }
  .menu__link--active {
    color: var(--ifm-menu-color);
    /* border-left: var(--ifm-border-width) solid var(--ifm-toc-link-hover-color); */
  }
}

/* Base TOC styling (applies to both themes) */
.table-of-contents__link {
  transition: color var(--ifm-transition-fast) ease;
  opacity: 0.8;
}

.table-of-contents__link:hover {
  opacity: 1;
  text-decoration: none;
}

/* Right TOC Styling */
.table-of-contents {
  line-height: 1.6; /* More vertical space */
}

/* TOC links */
.table-of-contents__link, .table-of-contents li {
  font-weight: 300; /* Lighter than headings */
  padding: 0rem 0.5rem; /* Reduced padding */
  display: block; /* Ensure consistent spacing */
}

.table-of-contents__link, .table-of-contents li, .table-of-contents__link strong {
  font-weight: 300;
}

/* Active TOC item */
.table-of-contents__link--active {
  border-left: var(--ifm-border-width) solid var(--ifm-color-primary); /* Visual indicator */
}

/* Hover effects */
.table-of-contents__link:hover {
  opacity: 0.8;
  transform: translateX(1px); /* Subtle movement */
  transition: all 0.2s ease;
}

/* Reduce crowding */
.table-of-contents ul {
  padding-left: 0.8rem; /* Less indentation */
}

/*Lighten font */
.table-of-contents__link {
  color: var(--ifm-color-emphasis-600); /* Softer than default text */
}

/* Add section spacing */
.table-of-contents li {
  margin-bottom: 0.4rem; /* Space between major sections */
}

/* Enable ligatures */
code:not(.codeBlockLines_e6Vv) {
  border: 0px;
  padding: 0.3rem 0.4rem;
  color: var(--ifm-color-primary-lightest);
  box-shadow: var(--ifm-global-shadow-lw);
  border-radius: 4px;
  font-weight: 400;
  background-color:  var(--ifm-pre-background);
}

/* Force headings to use heading font */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--ifm-heading-font-family);
  font-weight: 500; /* Match your @font-face weight */
}

h1 {
  font-weight: 600;
  font-size: 2.5em;
}

h2 {
  font-size: 2em;
  font-weight: 600;
}

h3 {
  font-size: 1.5em;
}

h4 {
  font-size: 1.2em;
}

/* ===== FORCE BACKGROUNDS ===== */
/* Light theme containers */
:root {
  html, body,
  #__docusaurus,
  .main-wrapper,
  .theme-doc-markdown {
    background-color: var(--ifm-background-color);
  }
}

:root {
  .navbar {
    background-color: var(--ifm-background-color);
    box-shadow: none;
    border-bottom: 1px solid var(--ifm-color-subtle);
  }
  .navbar__link {
    color: var(--ifm-font-color-base);
    transition: color 0.2s ease;
  }
  .navbar__link:hover {
    color: #19dfae;
    opacity: 1;
  }
}

/* Dark theme containers */
:root[data-theme='dark'] {
  html, body,
  #__docusaurus,
  .main-wrapper,
  .theme-doc-markdown,
  .navbar{
    background-color: var(--ifm-background-color);
  }
}

/* GitBook-style Image Zoom */
.zoom-container {
  display: inline-block; /* Maintains document flow */
  cursor: zoom-in;
}

.zoom-image {
  transition: transform 0.2s ease;
}

.zoom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.zoom-overlay.active {
  opacity: 1;
  pointer-events: all;
}

.zoom-content {
  max-width: 90vw;
  max-height: 90vh;
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.zoom-overlay.active .zoom-content {
  transform: scale(1);
}

.zoom-close {
  position: fixed;
  top: 24px;
  right: 24px;
  color: white;
  font-size: 28px;
  cursor: pointer;
  z-index: 1001;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.zoom-overlay.active ~ .zoom-close {
  opacity: 1;
}

/* Prevent scrolling when zoomed */
body.zoom-active {
  overflow: hidden;
}

