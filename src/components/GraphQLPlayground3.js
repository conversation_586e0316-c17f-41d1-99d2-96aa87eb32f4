import React from 'react';
import { Graphi<PERSON> } from 'graphiql';
import { createGraphiQLFetcher } from '@graphiql/toolkit';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import 'graphiql/graphiql.min.css';

export default function GraphQLPlayground({ query = '' }) {
  const { siteConfig } = useDocusaurusContext();

  // Get token from environment configuration
  const API_TOKEN = siteConfig.customFields.ONESOURCE_API_TOKEN;

  // Verify token is loaded
  if (!API_TOKEN) {
    return (
      <div style={{ 
        padding: '1rem', 
        background: '#ffebee', 
        color: '#c62828',
        borderRadius: '4px',
        margin: '1rem 0'
      }}>
        Error: API token is not configured. Please set ONESOURCE_API_TOKEN in your environment variables.
      </div>
    );
  }

  const customFetcher = async (graphQLParams) => {
    try {
      console.log('Using token from environment:', API_TOKEN ? '*****' + API_TOKEN.slice(-4) : 'MISSING');

      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-bp-token': API_TOKEN,
      };

      const response = await fetch(siteConfig.customFields.GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          query: graphQLParams.query,
          variables: graphQLParams.variables || undefined,
          operationName: graphQLParams.operationName || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.errors?.[0]?.message || 
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return response.json();
    } catch (error) {
      console.error('GraphQL Request Failed:', error.message);
      throw error;
    }
  };

  const fetcher = createGraphiQLFetcher({
    url: siteConfig.customFields.GRAPHQL_ENDPOINT,
    fetcher: customFetcher,
    subscriptionUrl: undefined,
  });

  return (
    <div style={{ 
      height: '600px',
      border: '1px solid #e1e1e1',
      borderRadius: '8px',
      overflow: 'hidden',
      margin: '2rem 0'
    }}>
      <GraphiQL 
        fetcher={fetcher}
        query={query}
        headerEditorEnabled={true}
        defaultHeaders={JSON.stringify({
          'x-bp-token': API_TOKEN
        })}
      />
    </div>
  );
}