import Loading from "@/app/loading";
import client from "@/gql/apolloClient";
import {
	OrderEnum,
	PostObjectsConnectionOrderbyEnum,
} from "@/types/generated";
import BlogIndexClient from "./BlogIndexClient";
import ClientWrapper from "../ClientWrapper";
import BlogList from "./BlogList";
import ResponsiveBlogPreview from "../CustomPageBlocks/BlogPreview/ResponsiveBlogPreview";
import getFilteredPosts from "./utils/getFilteredPosts";
import isPreview from "@/constants/isPreview";

export interface BlogIndexProps {
  numOfResults?: number
  showSort?: boolean
  tag?: string
  search?: string
  mobileCarousel?: boolean
  excludePosts?: string[]
  excludeTags?: string[]
}

export default async function BlogIndex({
	numOfResults = 12,
	showSort = false,
	tag,
	search,
	mobileCarousel = false,
	excludePosts,
	excludeTags
}: BlogIndexProps) {
	const { posts, error, loading } = await getFilteredPosts(
		client,
		numOfResults,
		"DATE" as PostObjectsConnectionOrderbyEnum,
		"DESC" as OrderEnum,
		search,
		tag,
		excludeTags,
		excludePosts,	
		isPreview
	);

	if (error) throw error;
	if (loading || !posts) return <Loading theme="light" height="30vh" />;
	
	const skipClient = !showSort && !tag && !search;

	if (skipClient) {
		if (mobileCarousel) {
			return <ResponsiveBlogPreview posts={posts} />;
		} else {
			return <BlogList posts={posts} />;
		}
	}

	return (
		<ClientWrapper>
			<BlogIndexClient
				initialPosts={posts}
				numOfResults={numOfResults}
				tag={tag}
				search={search}
				excludePosts={excludePosts}
				excludeTags={excludeTags}
				isPreview={isPreview}
			/>
		</ClientWrapper>
	);
}
