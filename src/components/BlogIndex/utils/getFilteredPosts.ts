import { GET_ALL_ONESOURCE_POSTS, GET_ALL_ONESOURCE_POSTS_BY_TAG } from "@/gql/wp-lp/queries";
import { OneSourceTagsToOneSourcePostConnectionEdge, OrderEnum } from "@/types/generated";

import { PostObjectsConnectionOrderbyEnum } from "@/types/generated";
import { NormalizedCacheObject } from "@apollo/client";
import { ApolloClient } from "@apollo/client";
import getDraftPosts from "./getDraftPosts";

export default async function getFilteredPosts(
	client: ApolloClient<NormalizedCacheObject>,
	first: number,
	field: PostObjectsConnectionOrderbyEnum,
	order: OrderEnum,
	search?: string,
	tag?: string,
	excludeTags?: string[],
	excludePosts?: string[],
	isPreview?: boolean
) {
	const tagIsNotDrafts = tag && tag !== "drafts";
	const query = tagIsNotDrafts ? GET_ALL_ONESOURCE_POSTS_BY_TAG : GET_ALL_ONESOURCE_POSTS;
	const sharedVariables = { first, field, order, search };
	const variables = tagIsNotDrafts
		? {
			...sharedVariables,
			id: tag,
		}
		: sharedVariables;
	
	const { data, error, loading } = await client.query({
		query: query,
		variables,
	});

	if (error) throw error;

	const tagPosts = data.oneSourceTags ? data.oneSourceTags?.oneSourcePosts : { edges: [] };

	const draftPosts = isPreview && !tagIsNotDrafts ? await getDraftPosts({ client, variables }) : { edges: [] };

	const filteredPosts = tagIsNotDrafts ? tagPosts : tag === "drafts" ? draftPosts : data.oneSourcePosts;

	let posts = [...draftPosts.edges, ...filteredPosts.edges];
	
	posts = posts.map(
		(edge: OneSourceTagsToOneSourcePostConnectionEdge) => edge.node
	);

	if (excludeTags && excludeTags.length > 0) {
		posts = posts.filter((post: { oneSourceTags?: { edges?: { node: { slug: string } }[] } }) => {
			const tags = post.oneSourceTags?.edges?.map((edge) => edge.node) || []; // Extract tags from edges
			console.log("Post Tags:", tags); // Log the extracted tags
			console.log("Exclude Tags:", excludeTags); // Log the excludeTags array
	
			const hasExcludedTag = tags.some((tag) => {
				console.log("Checking tag:", tag.slug); // Log each tag being checked
				return excludeTags.includes(tag.slug.toLowerCase());
			});
	
			return !hasExcludedTag;
		});
	}

	if (excludePosts && excludePosts.length > 0 && !tag) {
		posts = posts.filter((post: { slug: string }) => !excludePosts.includes(post.slug));
	}

	return { posts, error, loading };
}