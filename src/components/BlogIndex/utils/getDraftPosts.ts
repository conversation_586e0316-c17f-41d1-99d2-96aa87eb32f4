import { GET_ALL_ONESOURCE_POSTS_DRAFTS } from "@/gql/wp-lp/queries";
import { NormalizedCacheObject } from "@apollo/client";
import { PostObjectsConnectionOrderbyEnum } from "@/types/generated";
import { OrderEnum } from "@/types/generated";
import { ApolloClient } from "@apollo/client";

interface DraftPostsProps {
	client: ApolloClient<NormalizedCacheObject>;
	variables: {
		first: number;
		field: PostObjectsConnectionOrderbyEnum;
		order: OrderEnum;
		search?: string;
	};
}

export default async function getDraftPosts({ client, variables }: DraftPostsProps) {

	const { data} = await client.query({
		query: GET_ALL_ONESOURCE_POSTS_DRAFTS,
		variables,
	});

	const tagPosts = data.oneSourcePosts || { edges: [] };
	
	return tagPosts;
	
}


