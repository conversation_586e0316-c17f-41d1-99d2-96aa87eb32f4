"use client";
import { useEffect, useState } from "react";
import {
	OneSourcePost,
	PostObjectsConnectionOrderbyEnum,
	OrderEnum,
} from "@/types/generated";
import client from "@/gql/apolloClient";
import Loading from "@/app/loading";
import BlogList from "./BlogList";
import getFilteredPosts from "./utils/getFilteredPosts";

type BlogIndexClientProps = {
  initialPosts: OneSourcePost[]
  numOfResults: number
  tag?: string
  search?: string
  excludePosts?: string[]
  excludeTags?: string[]
  isPreview?: boolean
}

export default function BlogIndexClient({
	initialPosts,
	numOfResults,
	tag,
	search,
	excludeTags,
	excludePosts,
	isPreview
}: BlogIndexClientProps) {
	const [posts, setPosts] = useState<OneSourcePost[]>(initialPosts);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchPosts = async () => {
		setLoading(true);
		try {
			const { posts } = await getFilteredPosts(
				client,
				numOfResults,
				"DATE" as PostObjectsConnectionOrderbyEnum,
				"DESC" as OrderEnum,
				search,
				tag,
				excludeTags,
				excludePosts,
				isPreview
			);
			setPosts(posts);
		} catch (err) {
			setError("Error fetching sorted posts");
			console.error(err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchPosts();
	}, [tag]);

	if (error) throw error;
	if (loading || !posts) return <Loading theme="light" height="30vh" />;

	return (
		<BlogList posts={posts} />
	);
}
