import React from "react";
import { ContentNodeBlocksArgs } from "@/types";
import extractTagFromString from "@/utils/extractTagFromHtmlString";
import { Link } from "@chakra-ui/next-js";
import {  Image } from "@chakra-ui/react";
import ImageFullscreen from "./ImageFullscreen";

interface MediaBlocksProps {
    block: ContentNodeBlocksArgs;
}

export const imageStyles = {
	baseStyle: {
		width: "100%",
		height: "100%",
	},
};

export default function ImageBlock({ block }: MediaBlocksProps) {

	const content = block.originalContent;
	const img = content && typeof content === "string" ? extractTagFromString("img",content) : null;
	const alt = img && img.getAttribute("alt") || "";
	const src = img && img.getAttribute("src");
	const atts = block.attributes;
	const imgUrl = atts?.url || src || "";
	const displayWidth = atts?.displayWidth;
	const linkDestination = atts?.linkDestination;

	const image = (
		<ImageFullscreen src={imgUrl} alt={alt}>
			<Image
				src={imgUrl}
				alt={alt} 
				sx={{
					"--component-id": "core/image",
					...imageStyles.baseStyle,
				}}
				objectFit="cover"
				objectPosition="center"
				borderRadius={5}
				overflow="hidden"
				maxW={displayWidth ? displayWidth : "container.xl"}
					
			/>
		</ImageFullscreen>
	);


	if (linkDestination && linkDestination !== "none") return (
		<Link href={linkDestination}>{image}</Link>
	);


	return (image);
}