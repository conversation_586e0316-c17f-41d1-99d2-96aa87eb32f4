"use client";
import { Box, Image, Modal, ModalCloseButton, ModalContent, ModalOverlay, useDisclosure } from "@chakra-ui/react";

interface ImageFullscreenProps {
	src: string;
	alt: string;
	children: React.ReactNode;
}

export default function ImageFullscreen({ src, alt, children }: ImageFullscreenProps) {

	const { isOpen, onOpen, onClose } = useDisclosure();

	return (
		<Box onClick={onOpen} cursor={"zoom-in"}>
			<Modal isOpen={isOpen} onClose={onClose}>
				<ModalOverlay bg={"darkGreen.50"} backdropFilter='blur(10px) hue-rotate(90deg)' />
				<ModalContent justifyContent={"center"} alignSelf={"center"} alignItems={"center"} maxW={"90vw"} maxH={"90vh"} bg={"darkGreen.50"} onClick={onClose}>
					<ModalCloseButton />
					<Image src={src} alt={alt} w={"100%"} h={"100%"} objectFit={"contain"} cursor={"zoom-out"} />
				</ModalContent>
			</Modal>
			{children}
		</Box>
	);
}

