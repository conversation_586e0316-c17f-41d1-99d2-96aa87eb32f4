import { Checkbox, FormControl, FormErrorMessage, Text } from "@chakra-ui/react";
import { RepeaterGroupData } from "@/types";
import { Controller } from "react-hook-form";
import { Control } from "react-hook-form/dist/types/form";
import { FormData } from "./types";
import { FieldErrors } from "react-hook-form/dist/types/errors";

type ContactCheckboxProps = {
  checkbox: RepeaterGroupData
  control: Control<FormData, unknown>
  errors: FieldErrors<FormData>
  theme: "light" | "dark";
  name?: keyof FormData;
}

const ContactCheckbox = ({
	checkbox,
	control,
	errors,
	theme,
	name = "contactConsent"
}: ContactCheckboxProps) => {
	const { consent_message, is_contact_consent } = checkbox || {};

	if (is_contact_consent) {
		return (
			<FormControl isInvalid={!!errors[name as keyof FormData]}>
				<Controller
					name={name as keyof FormData}
					control={control}
					render={({ field }) => (
						<Checkbox
							name={field.name}
							ref={field.ref}
							isChecked={!!field.value} // use isChecked instead of checked
							onChange={e => field.onChange(e.target.checked)} // adapt the event
							alignItems="flex-start"
							variant={theme === "dark" ? "contactDark" : "default"}
							gap={1}
						>
							<Text as="div" fontSize="14px" lineHeight="125%">{consent_message}</Text>
						</Checkbox>
					)}
				/>
				<FormErrorMessage color="persimmon">
					{errors[name as keyof typeof errors]?.message as string}
				</FormErrorMessage>
			</FormControl>
		);
	}
};

export default ContactCheckbox;
