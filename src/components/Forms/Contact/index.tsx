"use client";
import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	FormControl,
	FormErrorMessage,
	useDisclosure
} from "@chakra-ui/react";
import ContactInput from "./ContactInput";
import ContactSelect from "./ContactSelect";
import ContactCheckbox from "./ContactCheckbox";
import { FormData, ContactFormModal } from "./types";
import { FormFields, RepeaterGroupData } from "@/types";
import { useEffect } from "react";
import { useForm, Controller } from "react-hook-form";
import SubmitModal from "./SubmitModal";
import { useRouter, useSearchParams } from "next/navigation";
import isExternalLink from "@/utils/isExternalLink";

type PipedriveContactFormProps = {
  submitButtonText: string;
  formFields: FormFields;
  formId: string;
  solutionOptions?: string[];
  successModal?: ContactFormModal;
  theme?: "light" | "dark";
  redirectUrl?: string;
  skipOptionText?: string;
  inGettingStartedFlow?: boolean;
  consentCheckboxes?: RepeaterGroupData[];
};

const PipedriveContactForm = ({
	submitButtonText,
	formFields,
	formId,
	solutionOptions,
	successModal,
	theme = "light",
	redirectUrl,
	skipOptionText,
	inGettingStartedFlow,
	consentCheckboxes
}: PipedriveContactFormProps) => {
	const { isOpen, onOpen, onClose } = useDisclosure();
	const router = useRouter();
	const searchParams = useSearchParams();
	const plan = searchParams.get("plan");
	const canSkip = skipOptionText && skipOptionText !== "" && redirectUrl && redirectUrl !== "";
	const canRedirect = inGettingStartedFlow && redirectUrl && redirectUrl !== "";
	const redirectExternal = canRedirect && isExternalLink(redirectUrl);
	const showSuccessModal = successModal && (!canRedirect || redirectExternal);

	function toBoolean(val: unknown): boolean {
		if (val === true || val === "true" || val === 1 || val === "1") return true;
		return false;
	}

	if (!consentCheckboxes) return null;

	const {
		handleSubmit,
		control,
		formState: { errors, isSubmitting },
		reset,
		setValue,
		watch
	} = useForm<FormData>({
		mode: "onBlur",
		defaultValues: consentCheckboxes?.reduce<Partial<FormData>>((acc, checkbox, idx) => {
			acc[`contactConsent_${idx}`] = toBoolean(checkbox.checked_by_default);
			return acc;
		}, {}),
	});

	useEffect(() => {
		const subscription = watch((values) => {
			if (consentCheckboxes && consentCheckboxes.length > 0) {
				const anyConsentChecked = consentCheckboxes.some((checkbox, idx) => {
					return checkbox.is_contact_consent && values[`contactConsent_${idx}`];
				});
				// Only update if value actually changes to avoid infinite loop
				if (values.contactConsent !== !!anyConsentChecked) {
					setValue("contactConsent", !!anyConsentChecked, { shouldValidate: true });
				}
			}
		});
		return () => subscription.unsubscribe();
	}, [consentCheckboxes, setValue, watch]);

	// Add this effect:
	useEffect(() => {
		if (consentCheckboxes) {
			reset(
				consentCheckboxes.reduce<Partial<FormData>>((acc, checkbox, idx) => {
					acc[`contactConsent_${idx}`] = toBoolean(checkbox.checked_by_default);
					return acc;
				}, {})
			);
		}
	}, [consentCheckboxes, reset]);

	const formattedFormId = JSON.stringify(formId)
		.replace(/\\r\\n/g, " ")
		.replace(/\\n/g, " ")
		.replace(/\\r/g, " ")
		.replace(/"/g, "");

	const submissionSource = String(`${formattedFormId} ${plan ? ` (${plan})` : ""}`);

	const onSubmit = async (data: FormData) => {
		data.source = submissionSource;
		try {
			const response = await fetch("/api/contact-submit", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({...data, consentCheckboxes}),
			});
		
			if (!response.ok) {
				throw new Error("Network response was not ok");
			}
		
			const result = await response.json();
			console.log("Server response:", result);
			onOpen();
			reset({
				name: "",
				email: "",
				solution: "",
				building: "",
				company: "",
				telegram: "",
				discord: "",
				contactConsent: true,
			});
	
			if (canRedirect) {
				if (isExternalLink(redirectUrl)) {
					window.open(redirectUrl, "_blank");
				} else {
					router.push(redirectUrl);
				}
			}
		} catch (error) {
			console.error("There was a problem with the fetch operation:", error);
			alert(`There was an error submitting the form. Please try again later. Error: ${error}`);
		}
	};

	const handleClose = () => {
		onClose();
		if (redirectUrl && !inGettingStartedFlow) {
			router.push(redirectUrl);
		} else {
			router.push("/");
		}
	};

	return (
		<>
			<form onSubmit={handleSubmit(onSubmit)}>
				<VStack w="100%" gap={12} alignItems="flex-start" opacity={isSubmitting ? 0.5 : 1} transition="opacity 0.3s ease-in-out">
					<VStack w="inherit" gap={6} alignItems="flex-start">
						<FormControl isInvalid={!!errors.name}>
							<Controller
								name="name"
								control={control}
								rules={{ required: "Name is required" }}
								render={({ field }) => (
									<ContactInput
										{...field}
										label="Your name"
										placeholder="OneSource"
										type="text"
										theme={theme}
										required
									/>
								)}
							/>
							<FormErrorMessage color="persimmon">{errors.name?.message}</FormErrorMessage>
						</FormControl>

						<FormControl isInvalid={!!errors.email}>
							<Controller
								name="email"
								control={control}
								rules={{ required: "Email is required", pattern: { value: /^\S+@\S+$/i, message: "Invalid email address" } }}
								render={({ field }) => (
									<ContactInput
										{...field}
										label="Email"
										placeholder="<EMAIL>"
										type="email"
										theme={theme}
										required
									/>
								)}
							/>
							<FormErrorMessage color="persimmon">{errors.email?.message}</FormErrorMessage>
						</FormControl>

						{formFields && formFields.map((field, index) => {
							if (field === "solution" && solutionOptions?.length) {
								return (
									<FormControl key={index} isInvalid={!!errors.solution}>
										<Controller
											name="solution"
											control={control}
											rules={{ required: "This field is required" }}
											render={({ field }) => (
												<ContactSelect
													{...field}
													label="What solutions are you exploring with OneSource?"
													options={solutionOptions}
													placeholder="Select type"
													theme={theme}
													required
												/>
											)}
										/>
										<FormErrorMessage color="persimmon">{errors.solution?.message}</FormErrorMessage>
									</FormControl>
								);
							} else if (field === "building") {
								return (
									<FormControl key={index} isInvalid={!!errors.building}>
										<Controller
											name="building"
											control={control}
											render={({ field }) => (
												<ContactInput
													{...field}
													label="Tell us what you're building"
													placeholder="A cool product"
													type="text"
													theme={theme}
												/>
											)}
										/>
										<FormErrorMessage color="persimmon">{errors.building?.message}</FormErrorMessage>
									</FormControl>
								);
							} else if (field === "company") {
								return (
									<FormControl key={index} isInvalid={!!errors.company}>
										<Controller
											name="company"
											control={control}
											rules={{ required: "Company is required" }}
											render={({ field }) => (
												<ContactInput
													{...field}
													label="Company"
													placeholder="BestWeb3"
													type="text"
													theme={theme}
													required
												/>
											)}
										/>
										<FormErrorMessage color="persimmon">{errors.company?.message}</FormErrorMessage>
									</FormControl>
								);
							} else if (field === "telegram") {
								return (
									<FormControl key={index} isInvalid={!!errors.telegram}>
										<Controller
											name="telegram"
											control={control}
											render={({ field }) => (
												<ContactInput
													{...field}
													label="Telegram"
													placeholder="telegram_username"
													type="text"
													theme={theme}
												/>
											)}
										/>
										<FormErrorMessage color="persimmon">{errors.telegram?.message}</FormErrorMessage>
									</FormControl>
								);
							} else if (field === "discord") {
								return (
									<FormControl key={index} isInvalid={!!errors.discord}>
										<Controller
											name="discord"
											control={control}
											render={({ field }) => (
												<ContactInput
													{...field}
													label="Discord"
													placeholder="discord_username"
													type="text"
													theme={theme}
												/>
											)}
										/>
										<FormErrorMessage color="persimmon">{errors.discord?.message}</FormErrorMessage>
									</FormControl>
								);
							} 
						})}
						{consentCheckboxes && consentCheckboxes.map((checkbox, index) => {
							console.log("Checkbox data:", checkbox);

							return ((
								<ContactCheckbox
									key={index}
									checkbox={checkbox}
									control={control}
									errors={errors}
									theme={theme}
									name={`contactConsent_${index}`}
								/>
							));
						})}
					</VStack>

					<VStack gap={4} w="100%" alignItems={canSkip ? "center" : "flex-start"}>
						<Button
							variant="primary"
							h={12}
							px="18px"
							py="13px"
							isLoading={isSubmitting}
							type="submit"
							w={canSkip ? "100%" : "auto"}
						>
							{submitButtonText || "Submit"}							
							{isSubmitting && <Spinner color="darkGreen.30" thickness="3px" speed="0.6s" ml={4} size="sm" />}
						</Button>
						{canSkip && <Link href={redirectUrl} target={isExternalLink(redirectUrl) ? "_blank" : ""} textAlign="center" color="darkGreen" _hover={{ textDecoration: "none", color: "dataGreen" }}>{skipOptionText}</Link>}
					</VStack>
				</VStack>
			</form>
			{showSuccessModal && <SubmitModal
				isOpen={isOpen}
				onClose={handleClose}
				content={successModal}
			/>}
		</>
	);
};

export default PipedriveContactForm;
