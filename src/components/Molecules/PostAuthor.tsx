import { Text, HStack } from "@chakra-ui/react";
import { User } from "@/types/generated";

interface PostAuthorProps {
    author: User
}

export const authorStyles = {
	baseStyle: {
		fontSize: "14px",
		fontWeight: "600px",
		color: "black",
		width: "100%",
		paddingTop: 4,
		paddingBottom: 4
	},
};


export default function PostAuthor({ author }: PostAuthorProps) {

	const authorName = author.firstName && author.lastName ? author.firstName + " " + author.lastName : author.name;

	return (
		<HStack __css={authorStyles.baseStyle} justifyContent={"center"} alignItems={"flex-start"} py={0}>
			<Text textStyle={"bodyXs"}>{authorName}</Text>
			{author.description && (
				<>
					<Text textStyle={"bodyXs"}> - </Text>
					<Text textStyle={"bodyXs"}>{author.description}</Text>
				</>
			)}
		</HStack>
	);
}