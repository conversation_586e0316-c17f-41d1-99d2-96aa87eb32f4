import React from "react";
import { Maybe } from "@/types/generated";
import { Text } from "@chakra-ui/react";


interface DateTextProps {
  date? : Maybe<string> | undefined
	readingTime?: string | undefined | null,
}

export const dateTextStyles = {
	baseStyle: {
		textStyle: "mono2Medium",
		color: "darkGreen",
		marginTop: 6,
		marginBottom: 2,
	},
};

export default function DateText({ date, readingTime }: DateTextProps) {
	
	if (!date) return null;

	const convertedDate = new Date(date);
	const formattedDate = convertedDate.toLocaleDateString("en-US", {
		day: "numeric",
		month: "short",
		year: "numeric",
	});

	return (
		<Text sx={{"--component-id": "core/date", ...dateTextStyles.baseStyle}}>
			{formattedDate}
			{readingTime && ` - ${readingTime} min read`}
		</Text>
	);
}

