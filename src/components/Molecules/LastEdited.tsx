import { Text } from "@chakra-ui/react";

interface LastEditedProps {
	lastEdited: string | null | undefined;
}

export default function LastEdited({ lastEdited }: LastEditedProps) {
	if (!lastEdited) return null;
	return (
		<Text textStyle={"bodyXs"} color="black" textAlign={"center"} pb={6}>
			Last updated {new Date(lastEdited).toLocaleDateString("en-US", {
				day: "numeric",
				month: "long",
				year: "numeric",
			})}
		</Text>
	);
}

