import React from "react";
import PostTitle from "./PostTitle";
import {VStack } from "@chakra-ui/react";
import DateText from "./Date";
import { OneSourceTags, User } from "@/types/generated";
import Tags from "./Tags";
import isPreview from "@/constants/isPreview";
import PostAuthor from "./PostAuthor";

interface PostHeaderProps {
  title: string;
  date: string;
  status: string,
	readingTime?: string | undefined | null,
	tags?: OneSourceTags[],
	showAuthor?: boolean,
	author?: User,
}

export const postHeaderStyles = {
	baseStyle: {
		marginY: 12,
	},
};

export default function PostHeader({
	title,
	date,
	readingTime,
	tags,
	status,
	showAuthor,
	author,
}: PostHeaderProps) {
	const isDraft = status === "draft" && isPreview;
	return (
		<VStack __css={postHeaderStyles.baseStyle} sx={{
			"--component-id": "core/post-header",
		}} maxW={"container.lg"} width={"100%"} >
			{tags && <Tags tags={tags} lightMode={true} />}
			<PostTitle>
				{isDraft ? (`[DRAFT] ${title}`) : title}
			</PostTitle>
			<DateText date={date} readingTime={readingTime} />
			{showAuthor && author && <PostAuthor author={author} />}
		</VStack>
	);
}
