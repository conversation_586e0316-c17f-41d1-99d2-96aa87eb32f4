import React from 'react';
import { GraphiQL } from 'graphiql';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import ExecutionEnvironment from '@docusaurus/ExecutionEnvironment';
import 'graphiql/graphiql.min.css';

export default function GraphQLPlayground({ 
  query = '',
  variables = '',
  height = '600px' 
}) {
  const { siteConfig } = useDocusaurusContext();

  if (!ExecutionEnvironment.canUseDOM) {
    return (
      <div style={{ 
        padding: '2rem',
        background: '#f5f5f5',
        borderRadius: '8px',
        textAlign: 'center'
      }}>
        GraphQL Playground will load when viewing this page in a browser
      </div>
    );
  }

  const customFetcher = async (graphQLParams) => {
    try {
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(graphQLParams),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.errors?.[0]?.message || 
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return response.json();
    } catch (error) {
      console.error('GraphQL Request Failed:', error.message);
      throw error;
    }
  };

  return (
    <div style={{ 
      height,
      border: '1px solid #e1e1e1',
      borderRadius: '8px',
      overflow: 'hidden',
      margin: '2rem 0'
    }}>
      <GraphiQL 
        fetcher={customFetcher}
        query={query}
        variables={variables}
        headerEditorEnabled={true}
      />
    </div>
  );
}