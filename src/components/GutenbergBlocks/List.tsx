import React from "react";
import { ContentNodeBlocksArgs } from "@/types";
import { Flex, ListItem, OrderedList, UnorderedList } from "@chakra-ui/react";

interface ListProps {
  block: ContentNodeBlocksArgs
  lightMode?: boolean
}

export const listStyles = {
	baseStyle: {
		lineHeight: "160%"
	},
};

export default function List({ block, lightMode }: ListProps) {
	const innerBlocks = block.attributes?.innerBlocks ?? block.innerBlocks;

	if (!innerBlocks) return null;
	return (
		<Flex flexDir="column" gap={2} maxW={"container.md"} w="100%" pl={3}>
			<RenderLists
				blocks={innerBlocks}
				ordered={block.attributes?.ordered ?? false}
				layer={1}
				lightMode={lightMode}
			/>
		</Flex>
	);
}

interface RenderListProps {
  blocks: ContentNodeBlocksArgs[]
  layer: number
  ordered?: boolean
  lightMode?: boolean
}

export function RenderLists({ blocks, layer, ordered, lightMode }: RenderListProps) {
	const listItems = () =>
		blocks.map((item: ContentNodeBlocksArgs, index: number) => {
			// Safely access originalContent using a type guard
			const content = item.attributes?.content || "";
			const originalContent =
                typeof item.attributes === "object" && "originalContent" in item.attributes
                	? (item.attributes as { originalContent?: string }).originalContent
                	: "";

			// Use originalContent if available, otherwise fallback to content
			const parsedContent = originalContent || content;

			// Check if parsedContent already contains nested lists
			const hasNestedLists = /<ul|<ol/.test(parsedContent);

			if (parsedContent?.length) {
				return (
					<ListItem
						key={`listItem-l${layer}-${index}`}
						w="100%"
						color={lightMode ? "darkGreen.90" : "white"}
						textStyle="body1Medium"
						lineHeight="160%"
						pl={"2px"}
						sx={{
							a: {
								color: "darkBlue",
								textDecoration: "underline",
								transition: "color .2s ease",
								_hover: { color: "blue" },
							},
							"ul, ol": {
								ml: 4,
								"li" : {
									pl: "2px"
								}
							},
							code: {
								bg: "toastedOat.35",
								fontSize: "0.875em",
								borderRadius: "4px",
								py: 1,
								px: 2
							}
						}}
					>
						{/* Render HTML content directly */}
						<span
							dangerouslySetInnerHTML={{
								__html: parsedContent,
							}}
						/>
						{/* Render nested lists only if parsedContent does not already contain them */}
						{!hasNestedLists && item.innerBlocks?.length && (
							<RenderLists
								blocks={item.innerBlocks}
								layer={layer + 1}
								lightMode={lightMode}
								ordered={false} // Force unordered for nested lists
							/>
						)}
					</ListItem>
				);
			} else if (item.innerBlocks?.length) {
				return (
					<RenderLists
						key={`listItem-l${layer}-${index}`}
						blocks={item.innerBlocks}
						layer={layer + 1}
						lightMode={lightMode}
						ordered={false} // Force unordered for nested lists
					/>
				);
			}
		});


	if (ordered) {
		return (
			<OrderedList
				sx={{
					"--component-id": "core/list",
				}}
				maxW="container.md"
				gap={2}
				w="100%"
				stylePosition="outside"
			>
				{listItems()}
			</OrderedList>
		);
	} else {
		return (
			<UnorderedList
				sx={{
					"--component-id": "core/list",
				}}
				gap={2}
				w="100%"
				maxW={"container.md"}
				stylePosition="outside"
			>
				{listItems()}
			</UnorderedList>
		);
	}
}
