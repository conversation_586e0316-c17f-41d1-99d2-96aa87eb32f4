import React from 'react';
import { Graphi<PERSON> } from 'graphiql';
import { createGraphiQLFetcher } from '@graphiql/toolkit';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import 'graphiql/graphiql.min.css';

export default function GraphQLPlayground({ query = '' }) {
  const { siteConfig } = useDocusaurusContext();

  // Verify configuration exists
  if (!siteConfig.customFields?.ONESOURCE_API_TOKEN) {
    return (
      <div style={{ 
        padding: '1rem', 
        background: '#fff8e1', 
        border: '1px solid #ffd54f',
        borderRadius: '4px',
        margin: '1rem 0'
      }}>
        <strong>Configuration Error:</strong> API token is not configured.
      </div>
    );
  }

  const customFetcher = async (graphQLParams) => {
    const endpoint = siteConfig.customFields.GRAPHQL_ENDPOINT;
    const token = siteConfig.customFields.ONESOURCE_API_TOKEN;

    try {
      console.log('Request headers:', {
        'Content-Type': 'application/json',
        'x-bp-token': token,
      });

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: new Headers({
          'Content-Type': 'application/json',
          'x-bp-token': token,
        }),
        body: JSON.stringify({
          query: graphQLParams.query,
          variables: graphQLParams.variables || null,
          operationName: graphQLParams.operationName || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.errors?.[0]?.message || `Request failed with status ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('Request failed:', {
        error: error.message,
        endpoint,
        tokenPresent: !!token,
      });
      throw error;
    }
  };

  const fetcher = createGraphiQLFetcher({
    url: siteConfig.customFields.GRAPHQL_ENDPOINT,
    fetcher: customFetcher,
    subscriptionUrl: undefined,
  });

  return (
    <div style={{ 
      height: '600px',
      border: '1px solid #e1e1e1',
      borderRadius: '8px',
      overflow: 'hidden',
      margin: '2rem 0'
    }}>
      <GraphiQL 
        fetcher={fetcher}
        query={query}
        headerEditorEnabled={true}
      />
    </div>
  );
}