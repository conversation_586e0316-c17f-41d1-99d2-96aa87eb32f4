import { Container, Box, VStack, Text } from "@chakra-ui/react";
import { ContentNodeBlocksArgs, CtaType, FormFields, RepeaterGroupData } from "@/types";
import ConnectIcons from "./ConnectIcons";
import GridBackground from "@/components/GridBackground";
import { transformRepeaterData } from "@/utils/transformRepeaterData";
import Instructions from "./Instructions";
import PartnershipBlock from "./PartnershipBlock";
import PipedriveContactForm from "@/components/Forms/Contact";

interface ConnectBoxProps {
  block: ContentNodeBlocksArgs;
  allBlocks: ContentNodeBlocksArgs[];
}

const ConnectBox = async ({ block, allBlocks }: ConnectBoxProps) => {
	const {
		title,
		description,
		variant,
		theme,
		instructions_call_to_action,
		instruction_title,
		partnership_logo,
		partnership_message,
		in_getting_started_flow,
		submit_button_text,
		form_success_modal_title,
		form_success_modal_description,
		form_success_modal_call_to_action,
		form_fields,
		redirect_url,
		skip_option_text,
		consent_checkboxes
	} = block.attributes?.data || {};

	const inGettingStartedFlow = in_getting_started_flow === "1";
	const showIcons = variant === "form" && !inGettingStartedFlow && theme === "light";
	const instructions = await transformRepeaterData(block, "instruction_steps");
	const solutionOptions = await transformRepeaterData(block, "solution_options") as { option: string }[];
	const consentCheckboxesData = consent_checkboxes ? await transformRepeaterData(block, "consent_checkboxes") : undefined;
	const consentCheckboxes = consentCheckboxesData as RepeaterGroupData[] | undefined;

	const solutionOptionValues = solutionOptions?.map((option: { option: string }) => option.option);
	const formFields = form_fields as unknown as FormFields;
	const showForm = variant === "form" && formFields && formFields.length > 0;
	const showInstructions = instructions && variant === "instructions";
	const instructionsCta = instructions_call_to_action as unknown as CtaType;
	const showPartnership = partnership_logo && partnership_message;
	const darkTheme = theme === "dark";
	const successModalCta = form_success_modal_call_to_action as unknown as CtaType;

	const isOnlyBlock = allBlocks.length === 1;

	const successModal = successModalCta ? {
		title: String(form_success_modal_title),
		description: String(form_success_modal_description),
		callToAction: {
			title: successModalCta.title,
			url: successModalCta.url,
			target: successModalCta.target,
		},
	} : undefined;

	return (
		<Box
			as="section"
			w="100%"
			bg={darkTheme ? "darkGreen" : "desertSand"}
			px={6}
			pt={{ base: 40, md: inGettingStartedFlow ? 40 : 80 }}
			zIndex={1}
			overflowX="hidden"
		>
			<Container
				maxW="container.sm"
				pb={inGettingStartedFlow ? 0 : 24}
				mb={inGettingStartedFlow ? 40 : 80}
				px={0}
				position="relative"
				zIndex={2}
			>
				<VStack color={darkTheme ? "desertSand" : "darkBrown"} w="100%">
					<VStack sx={{ p: { whiteSpace: "nowrap" } }} w="100%">
						{title && (
							<Text
								as={isOnlyBlock ? "h1" : "h2"}
								textStyle="title1"
								textAlign="center"
								whiteSpace={showInstructions ? "nowrap" : "normal"}
								dangerouslySetInnerHTML={{
									__html: title.replace("\r\n", "</br>"),
								}}
							/>
						)}
						{description && (
							<Text
								as="div"
								textStyle="body1Medium"
								mt={3}
								dangerouslySetInnerHTML={{ __html: description }}
							/>
						)}
						<Box
							my={12}
							bg={darkTheme ? "midnightGreen" : "white"}
							p={8}
							w="100%"
							borderRadius={8}
							border="1px solid"
							borderColor={darkTheme ? "dataGreen.15" : "cloudSync"}
						>
							{showForm && (
								<PipedriveContactForm
									submitButtonText={submit_button_text}
									formFields={formFields}
									formId={title}
									successModal={successModal}
									solutionOptions={solutionOptionValues}
									theme={theme as "light" | "dark"}
									redirectUrl={redirect_url}
									skipOptionText={skip_option_text}
									inGettingStartedFlow={inGettingStartedFlow}
									consentCheckboxes={consentCheckboxes}
								/>
							)}
							{showInstructions && (
								<Instructions
									instructions={instructions}
									instructionsCta={instructionsCta}
									instructionsTitle={instruction_title}
								/>
							)}
							{showPartnership && (
								<PartnershipBlock
									partnershipLogo={Number(partnership_logo)}
									partnershipMessage={partnership_message}
								/>
							)}
						</Box>
					</VStack>
				</VStack>
				{showIcons && <ConnectIcons />}
			</Container>
			{in_getting_started_flow && (
				<GridBackground
					height="100vh"
					color={darkTheme ? "dataGreen.15" : "toastedOat.35"}
					backgroundColor={darkTheme ? "darkGreen" : "desertSand"}
					zIndex={0}
					hasGradient={!darkTheme}
				/>
			)}
		</Box>
	);
};

export default ConnectBox;
