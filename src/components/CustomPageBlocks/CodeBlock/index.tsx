import { ContentNodeBlocksArgs } from "@/types";
import { Box, Container } from "@chakra-ui/react";
import detectLanguage from "@/utils/detectLanguage";
import formatCode from "@/utils/formatCode";
import FormattedCode from "./FormattedCode";

interface CodeProps {
  block: ContentNodeBlocksArgs
}

export default async function CodeBlock({ block }: CodeProps) {
	const { code_block, is_plain_text } = block.attributes?.data || {};

	const isPlainText = Number(is_plain_text) === 1;

	const language = isPlainText ? "plaintext" : detectLanguage(code_block?.toString() || "");
	const cleanedCode = isPlainText ? code_block : await formatCode(String(code_block), String(language));

	return (
		<Box sx={{ "&:not(last-of-type)": { mb: 6 } }} w="100%">
			<Container px={0} maxW="container.md">
				<Box bg="toastedOat.35" borderRadius={4} py={0} pr={1} pl={{ base: 6, md: 8 }}>
					<Box maxH="600px" overflowY="auto">
						<FormattedCode code={String(cleanedCode)} language={String(language)} />
					</Box>
				</Box>
			</Container>
		</Box>
	);
}
