"use client";
import {
	<PERSON>,
	<PERSON><PERSON>,
	Flex,
	<PERSON>con,
	<PERSON><PERSON><PERSON>,
	useClipboard,
	HStack,
} from "@chakra-ui/react";
import { FaRegCopy } from "react-icons/fa";
import { useState } from "react";

export default function FormattedCode({
	code,
	language,
}: {
  code: string
  language: string
}) {
	const [tooltipLabel, setTooltipLabel] = useState("Copy");
	const [isTooltipOpen, setIsTooltipOpen] = useState(false);
	const copiedCode = code.replace(/<\/?[^>]+(>|$)/g, "");
	const { onCopy } = useClipboard(String(copiedCode));

	const handleClick = () => {
		onCopy();
		setTooltipLabel("Copied!");
		setIsTooltipOpen(true);
		setTimeout(() => {
			setTooltipLabel("Copy");
			setIsTooltipOpen(false);
		}, 1000);
	};
    
	const handleMouseEnter = () => {
		if (!isTooltipOpen) {
			setIsTooltipOpen(true);
		}
	};
    
	const handleMouseLeave = () => {
		if (tooltipLabel === "Copy") {
			setIsTooltipOpen(false);
		}
	};
    
	return (
		<HStack w="100%" alignItems="flex-start" spacing={0} position="relative">
			<Box
				w="100%"
				py={6}
				fontSize={{ base: "14px", md: "16px" }}
				sx={{
					pre: {
						whiteSpace: "pre-wrap",
						backgroundColor: "transparent !important",
					},
					"pre, code, kbd, samp": {
						fontFamily: "monospace",
					}
				}}
				dangerouslySetInnerHTML={{
					__html: `<pre class="language-${language}"><code>${code}</code></pre>`,
				}}
			/>
			<Flex
				justifyContent="flex-end"
				m={0}
				p={0}
				pr={2}
				sx={{ "span.chakra-button__icon": { m: 0 } }}
				position="sticky"
				top={2}
				right={0}
			>
				<Tooltip
					label={tooltipLabel}
					placement="bottom"
					closeOnScroll={true}
					isOpen={isTooltipOpen}
					justifyContent="center"
					alignItems="center"
					px={2}
					py={0}
					bg="transparent"
					border="none"
					color="darkBrown.80"
					boxShadow="none"
					fontWeight="500"
					transform="translateY(-40px)"
					mt="-8px"
				>
					<Button
						variant="icon"
						color="darkBrown"
						border="none"
						bg="transparent"
						borderRadius={2}
						px={0}
						py={0}
						textAlign="center"
						onClick={handleClick}
						onMouseEnter={handleMouseEnter} // Handle hover in
						onMouseLeave={handleMouseLeave} // Handle hover out
						leftIcon={<Icon as={FaRegCopy} fontSize="20px" />}
						opacity={0.5}
						transition="all 0.2s ease-in-out"
						_hover={{ opacity: 1, bg: "darkBrown.4" }}
					/>
				</Tooltip>
			</Flex>
		</HStack>
	);
}
