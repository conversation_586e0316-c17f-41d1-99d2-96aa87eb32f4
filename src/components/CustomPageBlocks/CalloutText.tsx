import oneSourceTextStyles from "@/design/atoms/textAtoms";
import { ContentNodeBlocksArgs } from "@/types";
import { Container, HStack, Text } from "@chakra-ui/react";
import { decodeHTML } from "entities";

interface CalloutTextProps {
    block: ContentNodeBlocksArgs
}

type callOutSize =  "xs" | "small" | "big"

export const calloutTextStyles = {
	baseStyle: {
		lineHeight: "130%",
		color: "darkGreen",
	},
	variants: {
		xs: {
			...oneSourceTextStyles.body3Thin,
			fontWeight: "300"

		},
		small: {
			...oneSourceTextStyles.body1Medium,
			fontSize: { base: "16px", md: "18px" },
			"code": {
				bg: "dataGreen.30",
				px: 1,
				py: ".125rem",
				borderRadius: 1,
				fontSize: { base: "16px", md: "18px" }
			}
		},
		big: {
			...oneSourceTextStyles.headline2,
			fontSize: { base: "20px", md: "24px" },
			"code": {
				bg: "dataGreen.30",
				px: 2,
				py: 1,
				borderRadius: 1,
				fontSize: { base: "20px", md: "24px" },
				fontWeight: "500"
			}
		}
	}
};

export default function CalloutText({ block }: CalloutTextProps) {

	const blockSize = block.attributes?.data?.size as callOutSize;
	const size : callOutSize = blockSize || "big" as callOutSize;
	const isNotBig = size !== "big";

	const decodedText = decodeHTML(block.attributes?.data?.callout_text || "");

	const textStyles= calloutTextStyles.variants[size];

	return (
		<>
			<Container
				w={"100vw"}
				display={"flex"}
				justifyContent={"center"}
				alignItems={"center"}
				mx={0}
				my={2}
			>
				<HStack
					minW={{
						base: isNotBig ? "auto" : "calc(100vw - 40px)",
						lg: isNotBig ? "container.md" : "container.lg"
					}}
					maxW={ isNotBig ? "container.md" : "container.lg" }
					backgroundColor={"dataGreenLight"}
					borderRadius={6}
					m={{base: 2, md: 5}}
					py={8}
					px={size === "xs" ? 6 : 8}
				>
					<Text
						m={3}
						sx={{ ...calloutTextStyles.baseStyle, ...textStyles }}
						dangerouslySetInnerHTML={{ __html: decodedText }}
					/>
				</HStack>
			</Container>
		</>
	);
}