import {
	Box,
	Container,
	Text,
	VStack
} from "@chakra-ui/react";
import { ContentNodeBlocksArgs } from "@/types";
import { transformRepeaterData } from "@/utils/transformRepeaterData";
import ExpandableContent from "./ExpandableContent";

interface AccordionSectionProps {
  block: ContentNodeBlocksArgs,
  lightMode?: boolean
  blog?: boolean
}

const AccordionSection = async ({ block, lightMode = false, blog = false }: AccordionSectionProps) => {
	const { title, description } = block.attributes?.data || {};
	const expandables = await transformRepeaterData(block, "expandable_content");
	const numberedField = block.attributes?.data?.numbered;
	const numbered = numberedField ? Number(numberedField) : 1;

	return (
		<Box as="section" w="100%" px={{base: blog ? 0 : 1, sm: blog ? 0 : 4}}>
			<Container maxW="container.md" py={blog ? 8 : 24} px={0}>
				<VStack p={{base: blog ? 0 : 4, sm: blog ? 0 : 8}} borderRadius={4}>
					{title && (
						<Text as="h3" textStyle={blog ? "headline1" : "title3"} mb={numbered ? 10 : 2} w={blog ? "100%" : "auto"}>
							{title}
						</Text>
					)}
					{description && <Text>{description}</Text>}
					{expandables && <ExpandableContent expandables={expandables} lightMode={lightMode} numbered={numbered} />}
				</VStack>
			</Container>
		</Box>
	);
};

export default AccordionSection;
