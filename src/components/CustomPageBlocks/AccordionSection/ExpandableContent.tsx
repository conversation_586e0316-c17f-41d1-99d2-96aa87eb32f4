"use client";
import React, { useState } from "react";
import {
	Accordion,
	AccordionItem,
	AccordionButton,
	AccordionPanel,
	Text,
	HStack,
	Box,
	Image
} from "@chakra-ui/react";
import { AddIcon, MinusIcon } from "@chakra-ui/icons";
import { RepeaterGroupData } from "@/types";
import { parse } from "node-html-parser";

const ExpandableContent = ({
	expandables,
	lightMode = false,
	numbered = 1
}: {
  expandables: RepeaterGroupData[],
  lightMode?: boolean,
	numbered: number
}) => {
	const [expandedIndex, setExpandedIndex] = useState<number | number[]>([]);

	const handleAccordionChange = (index: number | number[]) => {
		setExpandedIndex(index);
	};
	
	const lightModeBorderColor = numbered ? "toastedOat" : "transparent";
	const lightModeBg = numbered ? "toastedOat.35" : "transparent";
	const lightModeTextColor = numbered ? "darkBrown" : "darkGreen";
	const expandedBorderColor = lightMode ? lightModeBorderColor : "dataGreen";

	return (
		<>
			{expandables?.length && (
				<Accordion allowMultiple onChange={handleAccordionChange} w="100%">
					{expandables.map((expandable, index) => {
						const isExpanded = Array.isArray(expandedIndex)
							? expandedIndex.includes(index)
							: expandedIndex === index;

						const parsedDescription = parse(String(expandable.description) || "");
						const itemBottomMargin = numbered ? 3 : isExpanded ? 2 : 1;

						return (
							<AccordionItem
								w="100%"
								key={index}
								bg={lightMode ? lightModeBg : "deepGreen"}
								border="1px solid"
								borderColor={isExpanded ? expandedBorderColor : "transparent"}
								transition="all 0.2s ease-in-out"
								borderRadius={numbered ? 6 : 0}
								mb={index === expandables.length - 1 ? 0 : itemBottomMargin}
							>
								<AccordionButton p="0" _hover={{ bg: "transparent"}}>
									<HStack
										alignItems="center"
										justifyContent="space-between"
										w="100%"
										p={numbered ? 4 : 1}
										gap={4}
									>
										{
											numbered ? (
												<HStack
													alignItems="center"
													justifyContent="center"
													border="1px solid"
													borderColor={lightMode ? "toastedOat" : "dataGreen.25"}
													transition="height 0.2s ease-in-out"
													borderRadius={4}
													flexBasis={6}
													minW={6}
													h={isExpanded ? 12 : 8}
												>
													<Text color={lightMode ? "toastedOat" : "dataGreen"} textStyle="body3Medium">
														{index + 1}
													</Text>
												</HStack>
											) : (
												<Image
													src={"/images/triangle-right.svg"}
													alt="Triangle Right"
													w={3}
													transform={isExpanded ? "rotate(90deg)" : "rotate(0deg)"}
												/>
											)
										}
										<Text
											as="h5"
											textStyle={numbered ? "body1Medium" : "headline3"}
											textAlign="left"
											w="100%"
										>
											{expandable.label}
										</Text>
										{
											numbered && (
												<HStack minW={8} h={8} justifyContent="center" alignItems="center">
													{isExpanded ? (
														<MinusIcon color={expandedBorderColor} />
													) : (
														<AddIcon color={expandedBorderColor} />
													)}
												</HStack>
											)
										}
									</HStack>
								</AccordionButton>
								<AccordionPanel pb={numbered ? 4 : 2} pt={0}>
									<Text
										textStyle="body2Medium"
										textAlign="left"
										color={lightMode ? lightModeTextColor : "desertSand.80"}
										pl={numbered ? 10 : 4}
										pr={4}
										sx={{ "a": {textDecoration: "underline", _hover: { color: "desertSand" }} }}
									>
										<Box as="span" dangerouslySetInnerHTML={{ __html: parsedDescription.toString() }} />
									</Text>
								</AccordionPanel>
							</AccordionItem>
						);
					})}
				</Accordion>
			)}
		</>
	);
};

export default ExpandableContent;
