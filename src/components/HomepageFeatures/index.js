import clsx from 'clsx';
import Heading from '@theme/Heading';
import styles from './styles.module.css';

const FeatureList = [
  {
    title: 'Real-time Data',
    imgSrc: require('@site/static/img/icon-real-time-data.png').default,
    description: (
      <>
        Tokens, metadata, balances, transactions, and contracts—everything you need, right at your fingertips.
      </>
    ),
  },
  {
    title: 'Media Caching',
    imgSrc: require('@site/static/img/icon-media-caching.png').default,
    description: (
      <>
        Retrieve cached NFT media instantly to ensure faster page loads, sharper visual quality, and a more streamlined workflow.
      </>
    ),
  },
  {
    title: 'GraphQL Queries',
    imgSrc: require('@site/static/img/icon-graphql.png').default,
    description: (
      <>
        A single request delivers everything you need—no more juggling endpoints, just effortless precision.
      </>
    ),
  },
];

function Feature({imgSrc, title, description}) {
  return (
    <div className={clsx('col col--4')}>
      <div className={styles.featureContainer}>  {/* New wrapper div */}
        <div className="text--center">
          <img 
            src={imgSrc} 
            className={styles.featureImg} 
            alt={title} 
          />
        </div>
      </div>
      <div className="text--center padding-horiz--md">
        <Heading as="h3">{title}</Heading>
        <p>{description}</p>
      </div>
    </div>
  );
}

export default function HomepageFeatures() {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className="row">
          {FeatureList.map((props, idx) => (
            <Feature key={idx} {...props} />
          ))}
        </div>
      </div>
    </section>
  );
}
