.features {
  display: flex;
  align-items: center;
  padding: 2rem 0;
  width: 100%;
}

/* For light theme */
[data-theme='light'] .featureContainer {
  background-color: #071f21; /* Your dark background color */
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

/* For dark theme - optional adjustment */
[data-theme='dark'] .featureContainer {
  background-color: var(--ifm-background-surface-color);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.featureImg {
  height: 200px;
  width: 200px;
  object-fit: contain;
}