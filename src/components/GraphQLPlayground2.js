import React from 'react';
import { GraphiQL } from 'graphiql';
import { createGraphiQLFetcher } from '@graphiql/toolkit';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import 'graphiql/graphiql.min.css';

export default function GraphQLPlayground({ query = '' }) {
  const { siteConfig } = useDocusaurusContext();

  // Hardcoded token - replace with your actual token
  const API_TOKEN = "BP-01JPMT831KBC098A63KXKK5V44";

  const customFetcher = async (graphQLParams) => {
    try {
      // Create headers object
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-bp-token': API_TOKEN,
      };

      // Log the headers being sent
      console.log('Request headers:', headers);

      const response = await fetch(siteConfig.customFields.GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          query: graphQLParams.query,
          variables: graphQLParams.variables || undefined,
          operationName: graphQLParams.operationName || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.errors?.[0]?.message || 
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return response.json();
    } catch (error) {
      console.error('GraphQL Request Failed:', error.message);
      throw error;
    }
  };

  // Create fetcher with our custom implementation
  const fetcher = createGraphiQLFetcher({
    url: siteConfig.customFields.GRAPHQL_ENDPOINT,
    fetcher: customFetcher,
    subscriptionUrl: undefined,
  });

  return (
    <div style={{ 
      height: '600px',
      border: '1px solid #e1e1e1',
      borderRadius: '8px',
      overflow: 'hidden',
      margin: '2rem 0'
    }}>
      <GraphiQL 
        fetcher={fetcher}
        query={query}
        headerEditorEnabled={true}
        defaultHeaders={JSON.stringify({
          'x-bp-token': API_TOKEN
        })}
      />
    </div>
  );
}