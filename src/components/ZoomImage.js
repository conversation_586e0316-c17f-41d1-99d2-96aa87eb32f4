import React, { useState, useRef, useEffect } from 'react';

export default function ZoomImage({ src, alt }) {
  const [isZoomed, setIsZoomed] = useState(false);
  const imgRef = useRef(null);
  const overlayRef = useRef(null);

  const handleZoom = () => {
    setIsZoomed(true);
    document.body.classList.add('zoom-active');
  };

  const handleClose = () => {
    setIsZoomed(false);
    document.body.classList.remove('zoom-active');
  };

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && isZoomed) {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isZoomed]);

  return (
    <>
      <div className="zoom-container" onClick={handleZoom}>
        <img
          ref={imgRef}
          className="zoom-image"
          src={src.default || src}
          alt={alt}
        />
      </div>

      {isZoomed && (
        <>
          <div 
            className="zoom-overlay active" 
            ref={overlayRef}
            onClick={handleClose}
          >
            <div className="zoom-content">
              <img
                src={src.default || src}
                alt={alt}
                style={{ maxWidth: '100%', maxHeight: '100%' }}
              />
            </div>
          </div>
          <div 
            className="zoom-close" 
            onClick={handleClose}
            aria-label="Close zoom"
          >
            &times;
          </div>
        </>
      )}
    </>
  );
}