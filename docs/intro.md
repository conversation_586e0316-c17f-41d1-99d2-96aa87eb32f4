---
description: 'OneSource: Powering Web3 with Scalable Backend Infrastructure'
id: intro
slug: /
---

# Overview

OneSource is a robust backend platform designed to empower Web3 applications with seamless, scalable, and high-performance data infrastructure. Whether you're building decentralized applications (dApps) or integrating on-chain data, OneSource ensures your projects are fast, reliable, and ready to scale as your user base grows.

***

## Key Features

### **GraphQL**

OneSource leverages GraphQL to give you precise control over the data you retrieve. Access exactly what you need, where you need it, for faster development and iteration.

### **High-Performance**

Our low-latency API ensures that your applications remain fast and responsive, even under heavy traffic. Retrieve data instantly and keep your users engaged.

### **Scalability**

As your user base grows, OneSource scales effortlessly to meet demand. Our infrastructure is designed to handle high traffic volumes without compromising performance.

### **Reliability & Uptime**

With 99.9% uptime, OneSource ensures that your operations run smoothly with minimal interruptions. Depend on us for consistent and reliable data access.

### **Media Caching**

Our cached NFT media ensures fast loading times and a great user experience for NFT-based applications.

***

## What You Can Do with OneSource

OneSource provides a comprehensive suite of query types to help you build and scale your Web3 applications:

* **Assets**: Retrieve detailed information about individual tokens and contracts, including ownership and distribution data.
* **Balances**: Gain insights into the distribution and ownership of digital assets across wallets.
* **Media**: Access token media details such as URIs, dimensions, and file types, with support for cached thumbnails in a variety of sizes.
* **Metadata**: Query metadata for tokens and contracts, including attributes, traits, creator details, and more with support for dynamic updates.

***

## Get Started

Ready to dive in? Check out [Getting Started](getting-started/README.md) to learn how to use OneSource and integrate it into your application. For detailed information about our API, explore the [OneSource Web3 API Reference](onesource-web3-api-reference/README.md).
