---
title: Account Access
---

import React, { useEffect } from 'react';
import BrowserOnly from '@docusaurus/BrowserOnly';
import ZoomImage from '@site/src/components/ZoomImage';

Your OneSource account dashboard can be accessed at [https://app.onesource.io/](https://app.onesource.io/).

:::tip
If you have not yet registered a OneSource account, you can do so by following the steps detailed in [Get an API Key](./getting-started/get-an-api-key).
:::

<ZoomImage
  src={require('/img/login.png')}
  alt="OneSource Account Login"
/>

Enter the email address with which you registered, complete the captcha, and click **Log in**. An email containing a link to log in to your dashboard will be distributed to the provided email address. Click the link in the email and you will be taken to your OneSource dashboard.

<ZoomImage
  src={require('/img/dashboard.png')}
  alt="OneSource Account Dashboard"
/>

Via your OneSource dashboard you can:

- View your existing API key(s)
- Generate a new API key
- Check the status of your subscription and API keys
- View your current cycle utilization and rate limit

## Alternative Account Access via AWS Marketplace

Your OneSource account dashboard can also be accessed via your AWS account.

Navigate to [OneSource Web3 API on AWS Marketplace](https://aws.amazon.com/marketplace/pp/prodview-at5xou6obw4s4) and login to your AWS account. At the top of the page there will be a blue banner indicating you've already subscribed. Click the **View subscription** button to view your AWS Marketplace subscriptions.

<ZoomImage
  src={require('/img/GetAnAPIKey8.png')}
  alt="OneSource Web3 API on AWS Marketplace"
/>

Find OneSource Web3 API in your list of subscriptions and in click **Set up product** in the rightmost **Actions** column to view your OneSource Web3 API subscription.

<ZoomImage
  src={require('/img/GetAnAPIKey9.png')}
  alt="AWS Marketplace Subscriptions"
/>

In the **Offer details** section atop the page, click the **vendor's website** link. This link will take you directly to your OneSource dashboard without having to login.

<ZoomImage
  src={require('/img/GetAnAPIKey10.png')}
  alt="OneSource Web3 API AWS Marketplace Subscription Page"
/>
