---
title: Integrating OneSource Queries
---
There are many ways to integrate OneSource GraphQL queries into your project. The method you choose should be based on the needs of your specific project.

This guide contains examples of how to integrate OneSource queries into your project using a handful of popular programming languages and tools.

Each example uses the [Get NFT Token Details](../getting-started/playgrounds/graphiql.mdx#get-nft-token-details) example query from the [Playgrounds](../getting-started/playgrounds) guides.

## JavaScript (Fetch API)

```javascript
const apiKey = "BP-XXXXXXXXXXXXXXXXXXXXXXXXXX";
const query = `
  query Token($contract: ID!, $tokenID: ID!) {
    token(contract: $contract, tokenID: $tokenID) {
      name
      description
      image {
        thumbnails(where: { preset: MEDIUM }) {
          url
        }
      }
      contract {
        id
        isERC721
        isERC1155
      }
    }
  }
`;

const variables = {
  contract: "NFT-contract-address-here",
  tokenID: "123"
};

fetch("https://api.onesource.io/nftindexer/mainnet/graphql", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "x-bp-token": apiKey
  },
  body: JSON.stringify({
    query,
    variables
  })
})
  .then((response) => response.json())
  .then((data) => console.log(data))
  .catch((error) => console.error("Error:", error));
```

## JavaScript (Axios)

```javascript
const axios = require("axios");

const apiKey = process.env.ONE_SOURCE_API_KEY;
const query = `
  query Token($contract: ID!, $tokenID: ID!) {
    token(contract: $contract, tokenID: $tokenID) {
      name
      description
      image {
        thumbnails(where: { preset: MEDIUM }) {
          url
        }
      }
      contract {
        id
        isERC721
        isERC1155
      }
    }
  }
`;

const variables = {
  contract: "NFT-contract-address-here",
  tokenID: "123"
};

axios.post(
  "https://api.onesource.io/nftindexer/mainnet/graphql",
  {
    query,
    variables
  },
  {
    headers: {
      "Content-Type": "application/json",
      "x-bp-token": apiKey
    }
  }
)
  .then((response) => console.log(response.data))
  .catch((error) => console.error("Error:", error));
```

## Python (Requests)

```python
import requests
import os

api_key = os.getenv("ONE_SOURCE_API_KEY")
url = "https://api.onesource.io/nftindexer/mainnet/graphql"

query = """
  query Token($contract: ID!, $tokenID: ID!) {
    token(contract: $contract, tokenID: $tokenID) {
      name
      description
      image {
        thumbnails(where: { preset: MEDIUM }) {
          url
        }
      }
      contract {
        id
        isERC721
        isERC1155
      }
    }
  }
"""

variables = {
  "contract": "NFT-contract-address-here",
  "tokenID": "123"
}

headers = {
    "Content-Type": "application/json",
    "x-bp-token": api_key
}

response = requests.post(url, json={"query": query, "variables": variables}, headers=headers)
print(response.json())
```

## GraphQL Clients (Apollo)

```javascript
import { ApolloClient, InMemoryCache, HttpLink } from "@apollo/client";
import { gql } from '@apollo/client';

const client = new ApolloClient({
  link: new HttpLink({
    uri: "https://api.onesource.io/nftindexer/mainnet/graphql",
    headers: { "x-bp-token": process.env.API_KEY }
  }),
  cache: new InMemoryCache()
});

const GET_TOKEN_DETAILS = gql`
  query Token($contract: ID!, $tokenID: ID!) {
    token(contract: $contract, tokenID: $tokenID) {
      name
      description
      image {
        thumbnails(where: { preset: MEDIUM }) {
          url
        }
      }
      contract {
        id
        isERC721
        isERC1155
      }
    }
  }
`;

const variables = {
  contract: "NFT-contract-address-here",
  tokenID: "123"
};

client.query({ query: GET_TOKEN_DETAILS, variables })
  .then((result) => console.log(result.data))
  .catch((error) => console.error("Error:", error));
```

## Best Practices

### Using Environment Variables for API Keys

To securely store and use your API key, we recommend using environment variables. This prevents hardcoding sensitive information in your code and makes it easier to manage keys across different environments.

#### Step 1: Set the Environment Variable

* **For Local Development**: Create a `.env` file and add your API key (or set the environment variable in your system).

Create a `.env` file:

```text
API_KEY=BP-12345678912345678912345678
```

Set the environment variable in your system:

```bash
export API_KEY=BP-12345678912345678912345678
```

* **For Production**: Use a secret management tool like AWS Secrets Manager or Azure Key Vault.

#### Step 2: Access the Environment Variable

**JavaScript**:

```javascript
const apiKey = process.env.API_KEY;
```

**Python**:

```python
import os
api_key = os.getenv("API_KEY")
```

### Handle Errors Gracefully

Check for errors in the response and provide meaningful feedback.

### Optimize Queries

Use only the query fields you need to reduce response size and improve performance in your project.

## Other Ways to Use GraphQL

### Relay (React)

Relay is a high-performance GraphQL client for React.

### Server-Side Frameworks

Integrate GraphQL into server-side applications using frameworks like Express (Node.js) or Django (Python).

### Mobile Development

Use GraphQL in mobile apps with Apollo Client (React Native) or Relay.

### Static Site Generators

Fetch data at build time or runtime in static sites built with Next.js or Gatsby.

### Low-Code/No-Code Platforms

Integrate GraphQL into platforms like Zapier, Make or Retool.