---
layout:
  title:
    visible: true
  description:
    visible: true
  tableOfContents:
    visible: true
  outline:
    visible: true
  pagination:
    visible: true
title: Queries
---
The root `Query` type is the entry point for all read operations in the OneSource Web3 API. Each field represents a distinct queryable resource (e.g., `tokens`, `contracts`), with pagination and filtering support.

## [`Balance`](balance)

Get token holdings for a user wallet or contract address.

## [`Balances`](balances)

Fetch a list of balances with pagination and sorting.

## [`Contract`](contract)

Get details of a single smart contract.

## [`Contracts`](contracts)

Fetch a list of contracts with pagination and sorting.

## [`Token`](token)

Get details and media for a single NFT token by contract address and token ID.

## [`Tokens`](tokens)

Fetch a list of NFTs and their details and media with pagination and sorting.
