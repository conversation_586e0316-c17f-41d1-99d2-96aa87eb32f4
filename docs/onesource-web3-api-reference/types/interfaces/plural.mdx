---
id: plural
title: Plural
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
interface Plural {
  count: UInt64!
  remaining: UInt64!
  cursor: String
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Plural.<b>count</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>UInt64!</b></code>](/onesource-web3-api-reference/types/scalars/uint-64.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Plural.<b>remaining</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>UInt64!</b></code>](/onesource-web3-api-reference/types/scalars/uint-64.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Plural.<b>cursor</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 







### Implemented By

[`Balances`](/onesource-web3-api-reference/types/objects/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Contracts`](/onesource-web3-api-reference/types/objects/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>