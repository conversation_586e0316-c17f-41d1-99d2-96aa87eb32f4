---
id: balance
title: Balance
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Balance` object represents a user's or contract's holdings of a specific token/NFT. This object provides crucial ownership details, including the token amount held, associated smart contract information, and token metadata where applicable.
### Example Query

```graphql
query GetBalance($owner: ID!, $contract: ID!) {
  balance(owner: $owner, contract: $contract) {
    value
    token {
      name
    }
    contractType
    owner
    contract {
      decimals
    }
  }
}
```

### Understanding the `value` Field

The `value` field returns `ERC-20` token balances **exactly as stored on the Ethereum blockchain** - as a raw, unformatted integer string representing the smallest unit of the token. This preserves full precision without decimal approximation.

You will need to convert the `value` to its decimal format using the token's `decimals`.

```javascript
// Requires knowing the token's decimals
const formatted = value / (10 ** tokenDecimals);
```

The **ether.js** and **web3.js** libraries can also help with these conversions.

### Use Cases

* Checking ownership of specific NFTs.
* Calculating total holdings of a particular token.
* Building portfolio dashboards.

### Implementation Notes

* The `value` field returns strings to support arbitrary precision.
* For ERC-20 tokens, `token` will be null.
* Use with the `Balances` type for paginated results.
* Combine with the `Tokens` type to get complete metadata when displaying NFT collections.

### Query Format



```graphql
type Balance {
  owner: String!
  contractType: ContractType!
  contract: Contract!
  token: Token
  value: String!
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Balance.<b>owner</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String!</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The address of the owner of the balance.


#### [<code style={{ fontWeight: 'normal' }}>Balance.<b>contractType</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractType!</b></code>](/onesource-web3-api-reference/types/enums/contract-type.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="enum"/> 
The type of contract (`ERC-721`, `ERC-1155`, `ERC-20`).


#### [<code style={{ fontWeight: 'normal' }}>Balance.<b>contract</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Contract!</b></code>](/onesource-web3-api-reference/types/objects/contract.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 
The contract object.


#### [<code style={{ fontWeight: 'normal' }}>Balance.<b>token</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Token</b></code>](/onesource-web3-api-reference/types/objects/token.mdx) <Badge class="badge badge--secondary " text="object"/> 
The token object (if applicable).


#### [<code style={{ fontWeight: 'normal' }}>Balance.<b>value</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String!</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The balance value.






### Returned By

[`balance`](/onesource-web3-api-reference/operations/queries/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>

### Member Of

[`Balances`](/onesource-web3-api-reference/types/objects/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>