---
id: contracts
title: Contracts
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Contracts` object provides a **paginated, cursor-based list** of smart contracts (`Contract` objects). It enables efficient traversal of large contract datasets while tracking pagination state via `cursor` and `remaining` fields.
### Example Query

```graphql
query Contracts($first: Int, $where: ContractFilter, $orderBy: ContractOrderBy, $orderDirection: OrderDirection) {
  contracts(first: $first, where: $where, orderBy: $orderBy, orderDirection: $orderDirection) {
    contracts {
      symbol
      name
      createdAt
      id
    }
  }
}
```

### Use Cases

* Contract discovery (browse all contracts on a given chain and filter by type).
* Analytics dashboards (track holder distribution and contract deployment).
* Multi-Contract UIs (display ranked lists on a marketplace frontend).

### Query Format



```graphql
type Contracts implements Plural {
  count: UInt64!
  remaining: UInt64!
  cursor: String
  contracts: [Contract!]!
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Contracts.<b>count</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>UInt64!</b></code>](/onesource-web3-api-reference/types/scalars/uint-64.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Total contracts matching query.


#### [<code style={{ fontWeight: 'normal' }}>Contracts.<b>remaining</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>UInt64!</b></code>](/onesource-web3-api-reference/types/scalars/uint-64.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Contracts left to fetch.


#### [<code style={{ fontWeight: 'normal' }}>Contracts.<b>cursor</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Pagination key (null if no more results).


#### [<code style={{ fontWeight: 'normal' }}>Contracts.<b>contracts</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>[Contract!]!</b></code>](/onesource-web3-api-reference/types/objects/contract.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 
List of contracts with full details.


### Interfaces

#### [<code style={{ fontWeight: 'normal' }}><b>Plural</b></code>](/onesource-web3-api-reference/types/interfaces/plural.mdx) <Badge class="badge badge--secondary " text="interface"/> 







### Returned By

[`contracts`](/onesource-web3-api-reference/operations/queries/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>