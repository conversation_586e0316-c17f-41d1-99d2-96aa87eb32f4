---
id: image
title: Image
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Image` object provides detailed information about the original
media associated with an NFT.

### Query Format



```graphql
type Image {
  status: ImageStatus!
  url: String!
  contentType: String!
  width: Int!
  height: Int!
  thumbnails(
    where: ThumbnailFilter
    orderBy: ThumbnailOrderBy
    orderDirection: OrderDirection
  ): [Thumbnail!]!
  createdAt: Time!
  errorMsg: String
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Image.<b>status</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ImageStatus!</b></code>](/onesource-web3-api-reference/types/enums/image-status.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="enum"/> 



#### [<code style={{ fontWeight: 'normal' }}>Image.<b>url</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String!</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Image.<b>contentType</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String!</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Image.<b>width</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Image.<b>height</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Image.<b>thumbnails</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>[Thumbnail!]!</b></code>](/onesource-web3-api-reference/types/objects/thumbnail.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 

##### [<code style={{ fontWeight: 'normal' }}>Image.thumbnails.<b>where</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ThumbnailFilter</b></code>](/onesource-web3-api-reference/types/inputs/thumbnail-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 



##### [<code style={{ fontWeight: 'normal' }}>Image.thumbnails.<b>orderBy</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ThumbnailOrderBy</b></code>](/onesource-web3-api-reference/types/enums/thumbnail-order-by.mdx) <Badge class="badge badge--secondary " text="enum"/> 



##### [<code style={{ fontWeight: 'normal' }}>Image.thumbnails.<b>orderDirection</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>OrderDirection</b></code>](/onesource-web3-api-reference/types/enums/order-direction.mdx) <Badge class="badge badge--secondary " text="enum"/> 



#### [<code style={{ fontWeight: 'normal' }}>Image.<b>createdAt</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Time!</b></code>](/onesource-web3-api-reference/types/scalars/time.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Image.<b>errorMsg</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 







### Member Of

[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>