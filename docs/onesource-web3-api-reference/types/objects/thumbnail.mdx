---
id: thumbnail
title: Thumbnail
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Thumbnail` object provides information about the thumbnails
(compressed versions of the original media) available via the OneSource Web3
API.

### Query Format



```graphql
type Thumbnail {
  preset: ThumbnailPreset!
  status: ThumbnailStatus!
  url: String!
  width: Int!
  height: Int!
  contentType: String!
  createdAt: Time!
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Thumbnail.<b>preset</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ThumbnailPreset!</b></code>](/onesource-web3-api-reference/types/enums/thumbnail-preset.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="enum"/> 



#### [<code style={{ fontWeight: 'normal' }}>Thumbnail.<b>status</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ThumbnailStatus!</b></code>](/onesource-web3-api-reference/types/enums/thumbnail-status.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="enum"/> 



#### [<code style={{ fontWeight: 'normal' }}>Thumbnail.<b>url</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String!</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Thumbnail.<b>width</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Thumbnail.<b>height</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Thumbnail.<b>contentType</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String!</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 



#### [<code style={{ fontWeight: 'normal' }}>Thumbnail.<b>createdAt</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Time!</b></code>](/onesource-web3-api-reference/types/scalars/time.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 







### Member Of

[`Image`](/onesource-web3-api-reference/types/objects/image.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>