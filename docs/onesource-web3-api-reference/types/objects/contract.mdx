---
id: contract
title: Contract
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Contract` object represents a contract address deployed on-chain. It contains comprehensive details about the smart contract.

### Example Query

```graphql
query AuditContract($contractId: ID!) {
  contract(id: $contractId) {
    id
    type
    name
    symbol
    holders
    createdAt
  }
}
```

### Use Cases

* Contract audits (fetch comprehensive details about a contract).
* Track total holders for community growth metrics.
* NFT discovery (filter contracts by token standard, name, etc.).

### Query Format



```graphql
type Contract {
  id: ID!
  type: ContractType!
  isERC20: Boolean!
  isERC721: Boolean!
  isERC1155: Boolean!
  name: String
  symbol: String
  decimals: Int
  supportsMetadata: Boolean!
  createdAt: Time!
  createdBlock: Int!
  holders: Int!
  tokens(
    first: Int = 10
    skip: Int = 0
    after: String
    where: TokenFilter
    orderBy: TokenOrderBy
    orderDirection: OrderDirection
  ): Tokens!
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>id</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ID!</b></code>](/onesource-web3-api-reference/types/scalars/id.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The contract address.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>type</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractType!</b></code>](/onesource-web3-api-reference/types/enums/contract-type.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="enum"/> 
The type of contract.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>isERC20</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean!</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Whether the contract is an `ERC-20` contract.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>isERC721</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean!</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Whether the contract is an `ERC-721` contract.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>isERC1155</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean!</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Whether the contract is an `ERC-1155` contract.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>name</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The name of the contract.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>symbol</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The symbol of the contract.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>decimals</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The number of decimals (for `ERC-20`).


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>supportsMetadata</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean!</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Whether the contract supports metadata.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>createdAt</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Time!</b></code>](/onesource-web3-api-reference/types/scalars/time.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
When the contract was created.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>createdBlock</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The block number at which the contract was created.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>holders</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The number of holders.


#### [<code style={{ fontWeight: 'normal' }}>Contract.<b>tokens</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Tokens!</b></code>](/onesource-web3-api-reference/types/objects/tokens.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 
The tokens in this contract.
##### [<code style={{ fontWeight: 'normal' }}>Contract.tokens.<b>first</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 



##### [<code style={{ fontWeight: 'normal' }}>Contract.tokens.<b>skip</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 



##### [<code style={{ fontWeight: 'normal' }}>Contract.tokens.<b>after</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 



##### [<code style={{ fontWeight: 'normal' }}>Contract.tokens.<b>where</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>TokenFilter</b></code>](/onesource-web3-api-reference/types/inputs/token-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 



##### [<code style={{ fontWeight: 'normal' }}>Contract.tokens.<b>orderBy</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>TokenOrderBy</b></code>](/onesource-web3-api-reference/types/enums/token-order-by.mdx) <Badge class="badge badge--secondary " text="enum"/> 



##### [<code style={{ fontWeight: 'normal' }}>Contract.tokens.<b>orderDirection</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>OrderDirection</b></code>](/onesource-web3-api-reference/types/enums/order-direction.mdx) <Badge class="badge badge--secondary " text="enum"/> 







### Returned By

[`contract`](/onesource-web3-api-reference/operations/queries/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>

### Member Of

[`Balance`](/onesource-web3-api-reference/types/objects/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Contracts`](/onesource-web3-api-reference/types/objects/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>