---
id: balances
title: Balances
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Balances` object provides a **paginated, cursor-based list** of token/NFT holdings (`Balance` objects) for a given user or contract address. It enables efficient traversal of large balance datasets while tracking pagination state via `cursor` and `remaining` fields.
### Example Query

```graphql
query GetBalances($owner: String!, $cursor: String) {
  balances(owner: $owner, first: 10, after: $cursor) {
    balances { owner, value, contract { id } }
    remaining
    cursor
  }
}
```

### Use Cases

* Displaying all owned NFTs/tokens.
* Calculating holdings across a collection.
* Paginated UIs (lazy-loading balances in chunks).

### Query Format



```graphql
type Balances implements Plural {
  count: UInt64!
  remaining: UInt64!
  cursor: String
  balances: [Balance!]!
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Balances.<b>count</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>UInt64!</b></code>](/onesource-web3-api-reference/types/scalars/uint-64.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Total balances matching the query.


#### [<code style={{ fontWeight: 'normal' }}>Balances.<b>remaining</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>UInt64!</b></code>](/onesource-web3-api-reference/types/scalars/uint-64.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Count of balances not yet returned in the current query.


#### [<code style={{ fontWeight: 'normal' }}>Balances.<b>cursor</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Pagination key for fetching the next batch (null if no more results).


#### [<code style={{ fontWeight: 'normal' }}>Balances.<b>balances</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>[Balance!]!</b></code>](/onesource-web3-api-reference/types/objects/balance.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 
List of individual token balances.


### Interfaces

#### [<code style={{ fontWeight: 'normal' }}><b>Plural</b></code>](/onesource-web3-api-reference/types/interfaces/plural.mdx) <Badge class="badge badge--secondary " text="interface"/> 







### Returned By

[`balances`](/onesource-web3-api-reference/operations/queries/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>

### Member Of

[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>