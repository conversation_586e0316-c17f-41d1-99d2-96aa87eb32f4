---
id: token
title: Token
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Token` object represents a **single NFT**. It provides comprehensive on-chain data, metadata, ownership details, and lifecycle events.

### Example Query

```graphql
query Query($contract: ID!, $tokenId: ID!) {
  token(contract: $contract, tokenID: $tokenId) {
    metadataContent
    name
    tokenID
    tokenURI
    description
    holders {
      balances {
        owner
      }
    }
  }
}
```

### Use Cases

* NFT Marketplaces (show NFT details like image, name, description).
* Wallet Dashboards (retrieve metadata and NFT media for display).
* Token Analytics (track mint/burn rates across collections).

### Query Format



```graphql
type Token {
  contract: Contract!
  tokenID: String!
  tokenURI: String
  tokenURIStatus: TokenURIStatus
  image: Image
  metadataStatus: MetadataStatus
  metadataContent: String
  metadataContentType: String
  createdAt: Time!
  createdBlock: Int!
  burnedAt: Time
  burnedBlock: Int
  errorMsg: String
  name: String
  description: String
  expired: Boolean
  dexTrades: Int!
  holders(
    first: Int = 10
    skip: Int = 0
    after: String
  ): Balances!
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Token.<b>contract</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Contract!</b></code>](/onesource-web3-api-reference/types/objects/contract.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 
The contract that minted this token.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>tokenID</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String!</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The token ID.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>tokenURI</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The token URI.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>tokenURIStatus</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>TokenURIStatus</b></code>](/onesource-web3-api-reference/types/enums/token-uristatus.mdx) <Badge class="badge badge--secondary " text="enum"/> 
The status of the token URI.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>image</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Image</b></code>](/onesource-web3-api-reference/types/objects/image.mdx) <Badge class="badge badge--secondary " text="object"/> 
The image associated with this token.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>metadataStatus</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>MetadataStatus</b></code>](/onesource-web3-api-reference/types/enums/metadata-status.mdx) <Badge class="badge badge--secondary " text="enum"/> 
The status of the metadata.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>metadataContent</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The metadata content.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>metadataContentType</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The content type of the metadata.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>createdAt</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Time!</b></code>](/onesource-web3-api-reference/types/scalars/time.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
When the token was created.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>createdBlock</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The block number at which the token was created.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>burnedAt</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Time</b></code>](/onesource-web3-api-reference/types/scalars/time.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
When the token was burned (if applicable).


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>burnedBlock</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The block number at which the token was burned (if applicable).


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>errorMsg</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Error message (if any).


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>name</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The name of the token.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>description</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The description of the token.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>expired</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Whether the token has expired.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>dexTrades</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int!</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Decentralized exchange trades involving this token.


#### [<code style={{ fontWeight: 'normal' }}>Token.<b>holders</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Balances!</b></code>](/onesource-web3-api-reference/types/objects/balances.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 
The quantity of holders of this token.
##### [<code style={{ fontWeight: 'normal' }}>Token.holders.<b>first</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 



##### [<code style={{ fontWeight: 'normal' }}>Token.holders.<b>skip</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 



##### [<code style={{ fontWeight: 'normal' }}>Token.holders.<b>after</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 







### Returned By

[`token`](/onesource-web3-api-reference/operations/queries/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>

### Member Of

[`Balance`](/onesource-web3-api-reference/types/objects/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Tokens`](/onesource-web3-api-reference/types/objects/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>