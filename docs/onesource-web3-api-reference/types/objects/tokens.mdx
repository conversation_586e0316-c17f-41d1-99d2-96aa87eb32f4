---
id: tokens
title: Tokens
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Tokens` object provides a **paginated, cursor-based list** of NFTs (`Token` objects). It enables efficient traversal of large NFT datasets while tracking pagination state via `cursor` and `remaining` fields.
### Example Query

```graphql
query Tokens($where: TokenFilter, $whereContract: ContractFilter, $first: Int) {
  tokens(where: $where, whereContract: $whereContract, first: $first) {
    tokens {
      name
      tokenID
      tokenURI
      metadataContent
      metadataContentType
      contract {
        name
        symbol
        type
      }
    }
  }
}
```

### Use Cases

* NFT Marketplaces (display token listings with metadata).
* Wallet Dashboards (show owned tokens across collections).
* Analytics Tools (analyze token distribution in a collection).

### Query Format



```graphql
type Tokens {
  count: UInt64!
  cursor: String
  tokens: [Token!]!
  timing: String
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>Tokens.<b>count</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>UInt64!</b></code>](/onesource-web3-api-reference/types/scalars/uint-64.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The total number of tokens


#### [<code style={{ fontWeight: 'normal' }}>Tokens.<b>cursor</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
The cursor for pagination


#### [<code style={{ fontWeight: 'normal' }}>Tokens.<b>tokens</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>[Token!]!</b></code>](/onesource-web3-api-reference/types/objects/token.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="object"/> 
The list of token objects


#### [<code style={{ fontWeight: 'normal' }}>Tokens.<b>timing</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Timing information






### Returned By

[`tokens`](/onesource-web3-api-reference/operations/queries/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>

### Member Of

[`Contract`](/onesource-web3-api-reference/types/objects/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>