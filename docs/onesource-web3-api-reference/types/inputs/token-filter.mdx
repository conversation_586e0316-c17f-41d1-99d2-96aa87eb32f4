---
id: token-filter
title: TokenFilter
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

```graphql
input TokenFilter {
  tokenID: String
  name: String
  name_like: String
  name_null: Boolean
  caseSensitive: Boolean
  metadataStatus: MetadataStatus
  metadataStatus_null: Boolean
  tokenURIStatus: TokenURIStatus
  burned: Boolean
  expired: Boolean
}
```

### Fields

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>tokenID</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>name</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>name_like</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>name_null</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>caseSensitive</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>metadataStatus</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>MetadataStatus</b></code>](/onesource-web3-api-reference/types/enums/metadata-status.mdx) <Badge class="badge badge--secondary " text="enum"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>metadataStatus_null</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>tokenURIStatus</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>TokenURIStatus</b></code>](/onesource-web3-api-reference/types/enums/token-uristatus.mdx) <Badge class="badge badge--secondary " text="enum"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>burned</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>TokenFilter.<b>expired</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

### Member Of

[`balances`](/onesource-web3-api-reference/operations/queries/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`tokens`](/onesource-web3-api-reference/operations/queries/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>