---
id: contract-filter
title: ContractFilter
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

```graphql
input ContractFilter {
  id: String
  name: String
  name_like: String
  name_null: Boolean
  decimals_null: Boolean
  error_null: Boolean
  type: ContractType
  is_erc20: Boolean
  is_erc721: Boolean
  is_erc1155: Boolean
  case_sensitive: Boolean
}
```

### Fields

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>id</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>name</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>name_like</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>name_null</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>decimals_null</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>error_null</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>type</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractType</b></code>](/onesource-web3-api-reference/types/enums/contract-type.mdx) <Badge class="badge badge--secondary " text="enum"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>is_erc20</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>is_erc721</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>is_erc1155</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

#### [<code style={{ fontWeight: 'normal' }}>ContractFilter.<b>case_sensitive</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/> 

### Member Of

[`balances`](/onesource-web3-api-reference/operations/queries/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`contracts`](/onesource-web3-api-reference/operations/queries/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`tokens`](/onesource-web3-api-reference/operations/queries/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>