---
id: image-filter
title: ImageFilter
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
input ImageFilter {
  status: ImageStatus
  status_null: Boolean
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>ImageFilter.<b>status</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ImageStatus</b></code>](/onesource-web3-api-reference/types/enums/image-status.mdx) <Badge class="badge badge--secondary " text="enum"/> 



#### [<code style={{ fontWeight: 'normal' }}>ImageFilter.<b>status_null</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Boolean</b></code>](/onesource-web3-api-reference/types/scalars/boolean.mdx) <Badge class="badge badge--secondary " text="scalar"/>