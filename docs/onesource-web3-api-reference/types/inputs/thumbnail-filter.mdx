---
id: thumbnail-filter
title: ThumbnailFilter
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
input ThumbnailFilter {
  preset: ThumbnailPreset
}
```




### Fields

#### [<code style={{ fontWeight: 'normal' }}>ThumbnailFilter.<b>preset</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ThumbnailPreset</b></code>](/onesource-web3-api-reference/types/enums/thumbnail-preset.mdx) <Badge class="badge badge--secondary " text="enum"/>