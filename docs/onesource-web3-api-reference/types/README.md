---
title: Types
---
*Types* define the shape and structure of queryable data in the OneSource Web3 API.

## `Enums`

Enums restrict a field to a fixed set of predefined values.

## `Inputs`

Inputs are used to pass complex data as arguments in queries.

## `Interfaces`

Interfaces enforce consistency across types.

## `Objects`

Objects define a category of data that can be queried.

## `Scalars`

Scalars represent single, indivisible values.

