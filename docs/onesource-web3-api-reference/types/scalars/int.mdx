---
id: int
title: Int
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.


```graphql
scalar Int
```








### Member Of

[`balances`](/onesource-web3-api-reference/operations/queries/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Contract`](/onesource-web3-api-reference/types/objects/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`contracts`](/onesource-web3-api-reference/operations/queries/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Image`](/onesource-web3-api-reference/types/objects/image.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Thumbnail`](/onesource-web3-api-reference/types/objects/thumbnail.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`tokens`](/onesource-web3-api-reference/operations/queries/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>