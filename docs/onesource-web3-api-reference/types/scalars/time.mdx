---
id: time
title: Time
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








`Time` uses the [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format to represent date and time. It is a string that follows the pattern `YYYY-MM-DDTHH:MM:SSZ`.

### Example

```graphql
"2023-01-01T00:00:00Z"
```



```graphql
scalar Time
```








### Member Of

[`Contract`](/onesource-web3-api-reference/types/objects/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Image`](/onesource-web3-api-reference/types/objects/image.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Thumbnail`](/onesource-web3-api-reference/types/objects/thumbnail.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>