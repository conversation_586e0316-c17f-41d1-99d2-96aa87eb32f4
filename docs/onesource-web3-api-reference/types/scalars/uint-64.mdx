---
id: uint-64
title: UInt64
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

```graphql
scalar UInt64
```

### Member Of

[`Balances`](/onesource-web3-api-reference/types/objects/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Contracts`](/onesource-web3-api-reference/types/objects/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Plural`](/onesource-web3-api-reference/types/interfaces/plural.mdx)  <Badge class="badge badge--secondary badge--relation" text="interface"/><Bullet />[`Tokens`](/onesource-web3-api-reference/types/objects/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>