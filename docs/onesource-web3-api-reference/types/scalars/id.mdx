---
id: id
title: ID
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `"4"`) or integer (such as `4`) input value will be accepted as an ID.


```graphql
scalar ID
```








### Member Of

[`balance`](/onesource-web3-api-reference/operations/queries/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`balances`](/onesource-web3-api-reference/operations/queries/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`contract`](/onesource-web3-api-reference/operations/queries/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Contract`](/onesource-web3-api-reference/types/objects/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`token`](/onesource-web3-api-reference/operations/queries/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>