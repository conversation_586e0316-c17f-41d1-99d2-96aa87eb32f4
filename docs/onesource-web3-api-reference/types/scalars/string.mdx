---
id: string
title: String
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.

```graphql
scalar String
```

### Member Of

[`balance`](/onesource-web3-api-reference/operations/queries/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Balance`](/onesource-web3-api-reference/types/objects/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`BalanceFilter`](/onesource-web3-api-reference/types/inputs/balance-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/><Bullet />[`balances`](/onesource-web3-api-reference/operations/queries/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Balances`](/onesource-web3-api-reference/types/objects/balances.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Contract`](/onesource-web3-api-reference/types/objects/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`ContractFilter`](/onesource-web3-api-reference/types/inputs/contract-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/><Bullet />[`contracts`](/onesource-web3-api-reference/operations/queries/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Contracts`](/onesource-web3-api-reference/types/objects/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Image`](/onesource-web3-api-reference/types/objects/image.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Plural`](/onesource-web3-api-reference/types/interfaces/plural.mdx)  <Badge class="badge badge--secondary badge--relation" text="interface"/><Bullet />[`Thumbnail`](/onesource-web3-api-reference/types/objects/thumbnail.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`TokenFilter`](/onesource-web3-api-reference/types/inputs/token-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/><Bullet />[`tokens`](/onesource-web3-api-reference/operations/queries/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Tokens`](/onesource-web3-api-reference/types/objects/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/>