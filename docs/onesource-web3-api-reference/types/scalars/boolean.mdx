---
id: boolean
title: Boolean
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};








The `Boolean` scalar type represents `true` or `false`.


```graphql
scalar Boolean
```








### Member Of

[`Contract`](/onesource-web3-api-reference/types/objects/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`ContractFilter`](/onesource-web3-api-reference/types/inputs/contract-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/><Bullet />[`ImageFilter`](/onesource-web3-api-reference/types/inputs/image-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/><Bullet />[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`TokenFilter`](/onesource-web3-api-reference/types/inputs/token-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/>