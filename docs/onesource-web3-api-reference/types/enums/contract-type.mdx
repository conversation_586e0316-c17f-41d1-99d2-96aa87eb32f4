---
id: contract-type
title: ContractType
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
enum ContractType {
  UNKNOWN
  HYBRID
  ERC1155
  ERC721
  ERC20
}
```




### Values

#### [<code style={{ fontWeight: 'normal' }}>ContractType.<b>UNKNOWN</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractType.<b>HYBRID</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractType.<b>ERC1155</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractType.<b>ERC721</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractType.<b>ERC20</b></code>](#)  







### Member Of

[`balance`](/onesource-web3-api-reference/operations/queries/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/><Bullet />[`Balance`](/onesource-web3-api-reference/types/objects/balance.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`Contract`](/onesource-web3-api-reference/types/objects/contract.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`ContractFilter`](/onesource-web3-api-reference/types/inputs/contract-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/>