---
id: thumbnail-preset
title: ThumbnailPreset
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
enum ThumbnailPreset {
  MICRO
  SMALL
  MEDIUM
  LARGE
  XLARGE
  ORIGINAL
  MICRO_VIDEO
  SMALL_VIDEO
  MEDIUM_VIDEO
  LARGE_VIDEO
  XLARGE_VIDEO
}
```




### Values

#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>MICRO</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>SMALL</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>MEDIUM</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>LARGE</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>XLARGE</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>ORIGINAL</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>MICRO_VIDEO</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>SMALL_VIDEO</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>MEDIUM_VIDEO</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>LARGE_VIDEO</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ThumbnailPreset.<b>XLARGE_VIDEO</b></code>](#)  







### Member Of

[`Thumbnail`](/onesource-web3-api-reference/types/objects/thumbnail.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`ThumbnailFilter`](/onesource-web3-api-reference/types/inputs/thumbnail-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/>