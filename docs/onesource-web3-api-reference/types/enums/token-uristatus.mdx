---
id: token-uristatus
title: TokenURIStatus
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
enum TokenURIStatus {
  OK
  EMPTY
  NOT_AVAILABLE
  NOT_SUPPORTED
  IS_IMAGE
  IS_METADATA
  SPECIAL
}
```




### Values

#### [<code style={{ fontWeight: 'normal' }}>TokenURIStatus.<b>OK</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenURIStatus.<b>EMPTY</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenURIStatus.<b>NOT_AVAILABLE</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenURIStatus.<b>NOT_SUPPORTED</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenURIStatus.<b>IS_IMAGE</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenURIStatus.<b>IS_METADATA</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenURIStatus.<b>SPECIAL</b></code>](#)  







### Member Of

[`Token`](/onesource-web3-api-reference/types/objects/token.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`TokenFilter`](/onesource-web3-api-reference/types/inputs/token-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/>