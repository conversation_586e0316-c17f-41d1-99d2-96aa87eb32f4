---
id: token-order-by
title: TokenOrderBy
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
enum TokenOrderBy {
  CONTRACT
  NAME
  TOKEN_ID
  CREATED_AT
  CREATED_BLOCK
  CLOSED_SWAP_ID
  COLLECTION_HOLDERS
  DEX_TRADES
}
```




### Values

#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>CONTRACT</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>NAME</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>TOKEN_ID</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>CREATED_AT</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>CREATED_BLOCK</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>CLOSED_SWAP_ID</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>COLLECTION_HOLDERS</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>TokenOrderBy.<b>DEX_TRADES</b></code>](#)  







### Member Of

[`tokens`](/onesource-web3-api-reference/operations/queries/tokens.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>