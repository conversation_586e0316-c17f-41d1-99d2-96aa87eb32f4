---
id: contract-order-by
title: ContractOrderBy
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
enum ContractOrderBy {
  ID
  NAME
  SYMBOL
  CREATED_AT
  CREATED_BLOCK
}
```




### Values

#### [<code style={{ fontWeight: 'normal' }}>ContractOrderBy.<b>ID</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractOrderBy.<b>NAME</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractOrderBy.<b>SYMBOL</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractOrderBy.<b>CREATED_AT</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ContractOrderBy.<b>CREATED_BLOCK</b></code>](#)  







### Member Of

[`contracts`](/onesource-web3-api-reference/operations/queries/contracts.mdx)  <Badge class="badge badge--secondary badge--relation" text="query"/>