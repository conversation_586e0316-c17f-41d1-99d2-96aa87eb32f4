---
id: image-status
title: ImageStatus
---




export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};











```graphql
enum ImageStatus {
  OK
  NOT_AVAILABLE
  NOT_RECOGNIZED
  TOO_LARGE
  RESET
}
```




### Values

#### [<code style={{ fontWeight: 'normal' }}>ImageStatus.<b>OK</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ImageStatus.<b>NOT_AVAILABLE</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ImageStatus.<b>NOT_RECOGNIZED</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ImageStatus.<b>TOO_LARGE</b></code>](#)  



#### [<code style={{ fontWeight: 'normal' }}>ImageStatus.<b>RESET</b></code>](#)  







### Member Of

[`Image`](/onesource-web3-api-reference/types/objects/image.mdx)  <Badge class="badge badge--secondary badge--relation" text="object"/><Bullet />[`ImageFilter`](/onesource-web3-api-reference/types/inputs/image-filter.mdx)  <Badge class="badge badge--secondary badge--relation" text="input"/>