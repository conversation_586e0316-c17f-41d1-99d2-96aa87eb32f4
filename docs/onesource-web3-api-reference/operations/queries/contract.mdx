---
id: contract
title: Contract
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

The `contract` query retrieves detailed information about a single smart contract, including its token standard (`ERC-721`/`ERC-1155`/`ERC-20`), metadata capabilities, and deployment details. This is essential for verifying contract properties before interacting with its tokens.

### Example

#### Basic Contract Verification

```graphql
query GetContractDetails($id: ID!) {
  contract(id: $id) {
    id
    name
    symbol
    isERC721
    isERC1155
    supportsMetadata
    createdAt
  }
}
```

#### Variables

```json
{
  "id": "0xbc4ca0eda7647a8ab7c2061c2e118a18a936f13d"
}
```

#### Response

```json
{
  "data": {
    "contract": {
      "id": "0xbc4ca0eda7647a8ab7c2061c2e118a18a936f13d",
      "name": "BoredApeYachtClub",
      "symbol": "BAYC",
      "isERC721": true,
      "isERC1155": false,
      "supportsMetadata": true,
      "createdAt": "2024-10-03T15:23:35Z"
    }
  }
}
```

### Common Use Cases

1. **Token Standard Verification**
  * Check `isERC721`/`isERC1155` before token interactions.
  * Verify `supportsMetadata` for UI rendering.
2. **Marketplace Listings**
  * Display contract name/symbol.
  * Show creation date for collection age.
3. **Developer Tooling**
  * Validate contracts before integration.
  * Check for proxy patterns.
4. **Analytics**
  * Track contract deployment trends.
  * Monitor metadata adoption rates.

### Best Practices

* **Cache Responses**: Contract details rarely change.
* **Error Handling**: Returns `null` for invalid addresses.
* **Combination Query**: Pair with `tokens` for full collection data.

### Related Queries

* `contracts`: For paginated lists of contracts.
* `tokens`: To explore a contract's tokens.

### Query Format

```graphql
contract(
  id: ID!
): Contract
```

### Arguments

#### [<code style={{ fontWeight: 'normal' }}>contract.<b>id</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ID!</b></code>](/onesource-web3-api-reference/types/scalars/id.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The contract address to query.

### Type

#### [<code style={{ fontWeight: 'normal' }}><b>Contract</b></code>](/onesource-web3-api-reference/types/objects/contract.mdx) <Badge class="badge badge--secondary " text="object"/> 
The `Contract` object represents a contract address deployed on-chain. It contains comprehensive details about the smart contract.

### Example Query

```graphql
query AuditContract($contractId: ID!) {
  contract(id: $contractId) {
    id
    type
    name
    symbol
    holders
    createdAt
  }
}
```

### Use Cases

* Contract audits (fetch comprehensive details about a contract).
* Track total holders for community growth metrics.
* NFT discovery (filter contracts by token standard, name, etc.).

