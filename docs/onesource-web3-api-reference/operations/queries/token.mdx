---
id: token
title: Token
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

The `token` query retrieves detailed information about a specific NFT or token, including its metadata, ownership history, and blockchain state. This is the primary method to fetch individual token data from collections.

### Example

#### Basic Token Fetch

```graphql
query Token($contract: ID!, $tokenId: ID!) {
  token(contract: $contract, tokenID: $tokenId) {
    tokenID
    name
    description
    image {
      url
    }
    contract {
      name
      symbol
    }
    holders {
      balances {
        owner
      }
    }
  }
}
```

#### Variables

```json
{
  "contract": "******************************************",
  "tokenId": "2341"
}
```

#### Response

This response is an example only and may not reflect current data.

```json
{
  "data": {
    "token": {
      "tokenID": "2341",
      "name": "Pudgy Penguin #2341",
      "description": "A collection 8888 Cute Chubby Pudgy Penquins sliding around on the freezing ETH blockchain.",
      "image": {
        "url": "QmNf1UsmdGaMbpatQ6toXSkzDpizaGmC9zfunCyoz1enD5/penguin/2341.png"
      },
      "contract": {
        "name": "PudgyPenguins",
        "symbol": "PPG"
      },
      "holders": {
        "balances": [
          {
            "owner": "******************************************"
          }
        ]
      }
    }
  }
}
```

### Common Use Cases

1. **NFT Display**: Show token details and media in marketplaces or galleries.
2. **Ownership Verification**: Confirm current holder of a specific token.
3. **Metadata Analysis**: Inspect token attributes and media.
4. **Historical Research**: Check creation/burn status.

### Best Practices

* **Error Handling**: Returns `null` for non-existent tokens.
* **Partial Data**: Some fields may be null if metadata is unavailable.
* **Combination Query**: Pair with `contract` query for full context.

### Related Queries

* `tokens`: For fetching multiple tokens from a collection.
* `contract`: To verify token standards before querying.

### Query Format

```graphql
token(
  contract: ID!
  tokenID: ID!
): Token
```

### Arguments

#### [<code style={{ fontWeight: 'normal' }}>token.<b>contract</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ID!</b></code>](/onesource-web3-api-reference/types/scalars/id.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Contract address where the token resides.

#### [<code style={{ fontWeight: 'normal' }}>token.<b>tokenID</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ID!</b></code>](/onesource-web3-api-reference/types/scalars/id.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Unique token identifier.

### Type

#### [<code style={{ fontWeight: 'normal' }}><b>Token</b></code>](/onesource-web3-api-reference/types/objects/token.mdx) <Badge class="badge badge--secondary " text="object"/> 
The `Token` object represents a **single NFT**. It provides comprehensive on-chain data, metadata, ownership details, and lifecycle events.

### Example Query

```graphql
query Query($contract: ID!, $tokenId: ID!) {
  token(contract: $contract, tokenID: $tokenId) {
    metadataContent
    name
    tokenID
    tokenURI
    description
    holders {
      balances {
        owner
      }
    }
  }
}
```

### Use Cases

* NFT Marketplaces (show NFT details like image, name, description).
* Wallet Dashboards (retrieve metadata and NFT media for display).
* Token Analytics (track mint/burn rates across collections).

