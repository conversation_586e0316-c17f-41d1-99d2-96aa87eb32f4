---
id: balance
title: Balance
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

The `balance` query retrieves **a specific token/NFT balance** for a given user or contract address. It returns
a single `Balance` object matching the provided filters.

### Example Query

#### Fetch ERC-1155 Balanc

```graphql

query GetNFTBalance($contract: ID!, $tokenId: String, $owner: ID!) {
  balance(contract: $contract, tokenID: $tokenId, owner: $owner) {
    owner
    value
    contract {
      name
      symbol
    }
    token {
      name
      tokenID
      description
    }
  }
}

```

#### Variables

```json

{
  "owner": "0x4182A46C61C3EE40E61304F8B419F813Eeced3b4",
  "contract": "0x76be3b62873462d2142405439777e971754e8e77",
  "tokenId": "10769"
}

```

#### Response

This response is an example only and may not reflect current data.

```json

{
  "data": {
    "balance": {
      "owner": "0x4182a46c61c3ee40e61304f8b419f813eeced3b4",
      "value": "6",
      "contract": {
        "name": "parallel",
        "symbol": "LL"
      },
      "token": {
        "name": "Wong, Purveyor of Curiosities",
        "tokenID": "10769",
        "description": "I can offer you unlimited knowledge and worldly pleasures. Once a upon a time this stuff used to cost a lot more than credits"
      }
    }
  }
}

```

### Common Use Cases

1. Verifying NFT ownership before allowing actions.
2. Checking token balances for wallet displays.
3. Validating fractional ownership (ERC-1155). 
4. Building allow lists based on token holdings.

### Best Practices

* Always include at least `owner` and `contract` arguments.
* For ERC-1155 tokens, check the `value` field for quantity owned.
* Combine with `contractType` filter when you only need specific token standards.
* Returns `null` when no matching balance is found.
* Returns an error if required arguments (`owner`, `contract`) are missing.
* May return partial data if metadata is unavailable.

### Related Queries

* `balances`: For paginated lists of all holdings.
* `contract`: To verify token standards before querying balances.

### Query Format

```graphql
balance(
  owner: ID!
  contract: ID!
  tokenID: String
  contractType: ContractType
): Balance
```

### Arguments

#### [<code style={{ fontWeight: 'normal' }}>balance.<b>owner</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ID!</b></code>](/onesource-web3-api-reference/types/scalars/id.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The user or contract address being checked.

#### [<code style={{ fontWeight: 'normal' }}>balance.<b>contract</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ID!</b></code>](/onesource-web3-api-reference/types/scalars/id.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
The contract address of the token or NFT.

#### [<code style={{ fontWeight: 'normal' }}>balance.<b>tokenID</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Specific token identifier (for `ERC-721` and `ERC-1155` contracts). Used when querying a particular NFT in a collection.

#### [<code style={{ fontWeight: 'normal' }}>balance.<b>contractType</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractType</b></code>](/onesource-web3-api-reference/types/enums/contract-type.mdx) <Badge class="badge badge--secondary " text="enum"/> 
Filter by token standard (`ERC-20`, `ERC-721`, `ERC-1155`). Used when you only want balances of a specific token type.

### Type

#### [<code style={{ fontWeight: 'normal' }}><b>Balance</b></code>](/onesource-web3-api-reference/types/objects/balance.mdx) <Badge class="badge badge--secondary " text="object"/> 
The `Balance` object represents a user's or contract's holdings of a specific token/NFT. This object provides crucial ownership details, including the token amount held, associated smart contract information, and token metadata where applicable.
### Example Query

```graphql
query GetBalance($owner: ID!, $contract: ID!) {
  balance(owner: $owner, contract: $contract) {
    value
    token {
      name
    }
    contractType
    owner
    contract {
      decimals
    }
  }
}
```

### Understanding the `value` Field

The `value` field returns `ERC-20` token balances **exactly as stored on the Ethereum blockchain** - as a raw, unformatted integer string representing the smallest unit of the token. This preserves full precision without decimal approximation.

You will need to convert the `value` to its decimal format using the token's `decimals`.

```javascript
// Requires knowing the token's decimals
const formatted = value / (10 ** tokenDecimals);
```

The **ether.js** and **web3.js** libraries can also help with these conversions.

### Use Cases

* Checking ownership of specific NFTs.
* Calculating total holdings of a particular token.
* Building portfolio dashboards.

### Implementation Notes

* The `value` field returns strings to support arbitrary precision.
* For ERC-20 tokens, `token` will be null.
* Use with the `Balances` type for paginated results.
* Combine with the `Tokens` type to get complete metadata when displaying NFT collections.

