---
id: tokens
title: Tokens
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

The `tokens` query retrieves a paginated list of NFT/token assets with flexible filtering and sorting. This is the primary method for fetching multiple tokens from collections or across the entire index.

### Example

#### Fetch First 10 Collection Tokens

```graphql
query Tokens($first: Int, $whereContract: ContractFilter, $orderBy: TokenOrderBy) {
  tokens(first: $first, whereContract: $whereContract, orderBy: $orderBy) {
    tokens {
      tokenID
      image {
        url
      }
      name
    }
  }
}
```

#### Variables

```json
{
  "first": "10",
  "whereContract": {
    "id": "******************************************"
  },
  "orderBy": "TOKEN_ID"
}
```

#### Response

This response is an example only and may not reflect current data.

```json
{
  "data": {
    "tokens": {
      "tokens": [
        {
          "tokenID": "1",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/1.png"
          },
          "name": "CloneX #1"
        },
        {
          "tokenID": "2",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/2.png"
          },
          "name": "CloneX #2"
        },
        {
          "tokenID": "3",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/3.png"
          },
          "name": "CloneX #3"
        },
        {
          "tokenID": "4",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/4.png"
          },
          "name": "CloneX #4"
        },
        {
          "tokenID": "5",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/5.png"
          },
          "name": "CloneX #5"
        },
        {
          "tokenID": "6",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/6.png"
          },
          "name": "CloneX #6"
        },
        {
          "tokenID": "7",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/7.png"
          },
          "name": "CloneX #7"
        },
        {
          "tokenID": "8",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/8.png"
          },
          "name": "CloneX #8301"
        },
        {
          "tokenID": "9",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/9.png"
          },
          "name": "CloneX #1174"
        },
        {
          "tokenID": "10",
          "image": {
            "url": "https://clonex-assets.rtfkt.com/images/10.png"
          },
          "name": "CloneX #19995"
        }
      ]
    }
  }
}
```

### Common Use Cases

1. **Collection Browsing**: Display tokens from a specific contract.
2. **Marketplace Listings**: Show filtered tokens for sale.
3. **Data Analysis**: Study token distributions/metadata.
4. **Wallet Integration**: Load all tokens owned by an address.

### Best Practices

* Always include `first` to limit response size.
* Use `where` filters before sorting for better performance.
* Request `count` only when needed for UI pagination.
* Combine with `contract` query for collection context.

### Related Queries

* `token`: For single token lookups.
* `contract`: To verify collection details.

### Query Format

```graphql
tokens(
  first: Int = 10
  skip: Int = 0
  after: String
  where: TokenFilter
  orderBy: TokenOrderBy
  orderDirection: OrderDirection
  whereContract: ContractFilter
  whereSwap: SwapFilter
): Tokens!
```

### Arguments

#### [<code style={{ fontWeight: 'normal' }}>tokens.<b>first</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Default value is 10. Number of tokens to return.

#### [<code style={{ fontWeight: 'normal' }}>tokens.<b>skip</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Default value is 0. Legacy pagination offset.

#### [<code style={{ fontWeight: 'normal' }}>tokens.<b>after</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Cursor for next page.

#### [<code style={{ fontWeight: 'normal' }}>tokens.<b>where</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>TokenFilter</b></code>](/onesource-web3-api-reference/types/inputs/token-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 
Filter by `metadataStatus`, `burned`, `name_like`, etc.

#### [<code style={{ fontWeight: 'normal' }}>tokens.<b>orderBy</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>TokenOrderBy</b></code>](/onesource-web3-api-reference/types/enums/token-order-by.mdx) <Badge class="badge badge--secondary " text="enum"/> 
`CONTRACT`, `COLLECTION_HOLDERS`, `TOKEN_ID`, `NAME`, `CREATED_AT`, `CREATED_BLOCK`

#### [<code style={{ fontWeight: 'normal' }}>tokens.<b>orderDirection</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>OrderDirection</b></code>](/onesource-web3-api-reference/types/enums/order-direction.mdx) <Badge class="badge badge--secondary " text="enum"/> 
`ASC`, `DESC`

#### [<code style={{ fontWeight: 'normal' }}>tokens.<b>whereContract</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractFilter</b></code>](/onesource-web3-api-reference/types/inputs/contract-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 
Filter by contract properties `id`, `name`, `type`, etc.

### Type

#### [<code style={{ fontWeight: 'normal' }}><b>Tokens</b></code>](/onesource-web3-api-reference/types/objects/tokens.mdx) <Badge class="badge badge--secondary " text="object"/> 
The `Tokens` object provides a **paginated, cursor-based list** of NFTs (`Token` objects). It enables efficient traversal of large NFT datasets while tracking pagination state via `cursor` and `remaining` fields.
### Example Query

```graphql
query Tokens($where: TokenFilter, $whereContract: ContractFilter, $first: Int) {
  tokens(where: $where, whereContract: $whereContract, first: $first) {
    tokens {
      name
      tokenID
      tokenURI
      metadataContent
      metadataContentType
      contract {
        name
        symbol
        type
      }
    }
  }
}
```

### Use Cases

* NFT Marketplaces (display token listings with metadata).
* Wallet Dashboards (show owned tokens across collections).
* Analytics Tools (analyze token distribution in a collection).

