---
id: balances
title: Balances
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

The `balances` query retrieves a paginated list of token/NFT holdings for user and contract addresses. It supports complex filtering across token standards and returns a `Balances!` object with cursor-based pagination.

### Example Query

#### Basic Wallet Holdings in Descending Order

```graphql
query GetWalletBalances($owner: ID!, $orderBy: BalanceOrderBy, $orderDirection: OrderDirection) {
  balances(owner: $owner, first: 20, orderBy: $orderBy, orderDirection: $orderDirection) {
    balances {
      value
      contract {
        name
        type
        id
      }
    }
    count
  }
}
```

#### Variables

```json
{
  "owner": "******************************************",
  "orderBy": "VALUE",
  "orderDirection": "DESC"
}
```

#### Response

This response is an example only and may not reflect current data.

```json
{
  "data": {
    "balances": {
      "balances": [
        {
          "value": "1",
          "contract": {
            "name": null,
            "type": "ERC721",
            "id": "******************************************"
          }
        },
        {
          "value": "1",
          "contract": {
            "name": "OpenEthereumToken",
            "type": "ERC721",
            "id": "******************************************"
          }
        }
      ],
      "count": 2
    }
  }
}
```

### Common Use Cases

1. Wallet dashboards showing all token holdings.
2. Bulk export of a collection's ownership data.
3. Access control based on token quantities.
4. Analytics on token distribution.

### Best Practices

* Prefer `after` over `skip` for large datasets.
* Combine `where` and `whereContract` for precise filtering.
* Always check `count` before fetching all results.
* Request only needed fields to improve performance.

### Related Queries

* `balance`: For single balance lookups.
* `token`: To explore token metadata separately.

### Query Format

```graphql
balances(
  first: Int = 10
  skip: Int = 0
  after: String
  owner: ID!
  where: BalanceFilter
  orderBy: BalanceOrderBy
  orderDirection: OrderDirection
  whereToken: TokenFilter
  whereContract: ContractFilter
): Balances!
```

### Arguments

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>first</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Default value is 10. Maximum items per page.

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>skip</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Default value is 0. Offset for legacy pagination.

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>after</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Cursor for next page (from previous response).

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>owner</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ID!</b></code>](/onesource-web3-api-reference/types/scalars/id.mdx) <Badge class="badge badge--secondary badge--non_null" text="non-null"/> <Badge class="badge badge--secondary " text="scalar"/> 
Required wallet/contract address to query.

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>where</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>BalanceFilter</b></code>](/onesource-web3-api-reference/types/inputs/balance-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 
Filter by balance-specific fields (`tokenID`, `contract`).

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>orderBy</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>BalanceOrderBy</b></code>](/onesource-web3-api-reference/types/enums/balance-order-by.mdx) <Badge class="badge badge--secondary " text="enum"/> 
`TYPE`, `CONTRACT`, `TOKEN_ID`, `ACCOUNT`, `VALUE`, `BLOCK`, `CONTRACT_TOKEN_ID`, `CONTRACT_ACCOUNT`, `CONTRACT_TOKEN_ID_ACCOUNT`

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>orderDirection</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>OrderDirection</b></code>](/onesource-web3-api-reference/types/enums/order-direction.mdx) <Badge class="badge badge--secondary " text="enum"/> 
`ASC`, `DESC`

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>whereToken</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>TokenFilter</b></code>](/onesource-web3-api-reference/types/inputs/token-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 
Filter by token properties (`tokenID`, `name`, etc.).

#### [<code style={{ fontWeight: 'normal' }}>balances.<b>whereContract</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractFilter</b></code>](/onesource-web3-api-reference/types/inputs/contract-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 
Filter by contract properties (`type`, `name`, etc.).

### Type

#### [<code style={{ fontWeight: 'normal' }}><b>Balances</b></code>](/onesource-web3-api-reference/types/objects/balances.mdx) <Badge class="badge badge--secondary " text="object"/> 
The `Balances` object provides a **paginated, cursor-based list** of token/NFT holdings (`Balance` objects) for a given user or contract address. It enables efficient traversal of large balance datasets while tracking pagination state via `cursor` and `remaining` fields.
### Example Query

```graphql
query GetBalances($owner: String!, $cursor: String) {
  balances(owner: $owner, first: 10, after: $cursor) {
    balances { owner, value, contract { id } }
    remaining
    cursor
  }
}
```

### Use Cases

* Displaying all owned NFTs/tokens.
* Calculating holdings across a collection.
* Paginated UIs (lazy-loading balances in chunks).

