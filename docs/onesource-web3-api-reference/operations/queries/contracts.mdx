---
id: contracts
title: Contracts
---

export const Bullet = () => <><span style={{ fontWeight: 'normal', fontSize: '.5em', color: 'var(--ifm-color-secondary-darkest)' }}>&nbsp;●&nbsp;</span></>

export const SpecifiedBy = (props) => <>Specification<a className="link" style={{ fontSize:'1.5em', paddingLeft:'4px' }} target="_blank" href={props.url} title={'Specified by ' + props.url}>⎘</a></>

export const Badge = (props) => <><span className={props.class}>{props.text}</span></>

import { useState } from 'react';

export const Details = ({ dataOpen, dataClose, children, startOpen = false }) => {
  const [open, setOpen] = useState(startOpen);
  return (
    <details {...(open ? { open: true } : {})} className="details" style={{ border:'none', boxShadow:'none', background:'var(--ifm-background-color)' }}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          setOpen((open) => !open);
        }}
        style={{ listStyle:'none' }}
      >
      {open ? dataOpen : dataClose}
      </summary>
      {open && children}
    </details>
  );
};

The `contracts` query retrieves a paginated list of smart contracts with filtering and sorting capabilities. This is your primary tool for discovering and analyzing NFT collections, token contracts, and their metadata across the blockchain.

### Example

#### 10 Newest ERC-1155 Contracts

```graphql
query Contracts($first: Int, $where: ContractFilter, $orderBy: ContractOrderBy, $orderDirection: OrderDirection) {
  contracts(first: $first, where: $where, orderBy: $orderBy, orderDirection: $orderDirection) {
    contracts {
      symbol
      name
      createdAt
      id
    }
  }
}
```

#### Variables

```json
{
  "first": "10",
  "where": {
    "is_erc1155": true
  },
  "orderBy": "CREATED_AT",
  "orderDirection": "DESC"
}
```

#### Response

This response is an example only and may not reflect current data.

```json
{
  "data": {
    "contracts": {
      "contracts": [
        {
          "symbol": "BZZ",
          "name": "BUZZ",
          "createdAt": "2025-03-14T13:19:35Z",
          "id": "0xce3a9dd08d4ec23efe955549ce82a2d9485c6c70"
        }
    ]
    }
  }
}
```

### Common Use Cases

1. **Collection Discovery**: Browse trending NFT projects.
2. **Developer Onboarding**: Find contracts for integration.
3. **Analytics**: Track contract deployment trends.
4. **Metadata Research**: Identify contracts with metadata support.

### Best Practices

* Use `where` filters to improve performance.
* Prefer `after` over `skip` for large datasets.
* Cache frequently accessed contract lists.
* Combine with `tokens` query for full collection analysis.

### Related Queries

* `contract`: For single contract details.
* `tokens`: To explore a contract's NFTs.

### Query Format

```graphql
contracts(
  first: Int = 10
  skip: Int = 0
  after: String
  where: ContractFilter
  orderBy: ContractOrderBy
  orderDirection: OrderDirection
): Contracts!
```

### Arguments

#### [<code style={{ fontWeight: 'normal' }}>contracts.<b>first</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Default value is 10. Number of contracts to return.

#### [<code style={{ fontWeight: 'normal' }}>contracts.<b>skip</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>Int</b></code>](/onesource-web3-api-reference/types/scalars/int.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Default value is 0. Legacy pagination offset.

#### [<code style={{ fontWeight: 'normal' }}>contracts.<b>after</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>String</b></code>](/onesource-web3-api-reference/types/scalars/string.mdx) <Badge class="badge badge--secondary " text="scalar"/> 
Cursor for next page.

#### [<code style={{ fontWeight: 'normal' }}>contracts.<b>where</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractFilter</b></code>](/onesource-web3-api-reference/types/inputs/contract-filter.mdx) <Badge class="badge badge--secondary " text="input"/> 
Filter by `is_erc20`, `is_erc721`, `is_erc1155`, `name_like`, etc.

#### [<code style={{ fontWeight: 'normal' }}>contracts.<b>orderBy</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>ContractOrderBy</b></code>](/onesource-web3-api-reference/types/enums/contract-order-by.mdx) <Badge class="badge badge--secondary " text="enum"/> 
`ID`, `NAME`, `SYMBOL`, `CREATED_AT`, `CREATED_BLOCK`

#### [<code style={{ fontWeight: 'normal' }}>contracts.<b>orderDirection</b></code>](#)<Bullet />[<code style={{ fontWeight: 'normal' }}><b>OrderDirection</b></code>](/onesource-web3-api-reference/types/enums/order-direction.mdx) <Badge class="badge badge--secondary " text="enum"/> 
`ASC`, `DESC`

### Type

#### [<code style={{ fontWeight: 'normal' }}><b>Contracts</b></code>](/onesource-web3-api-reference/types/objects/contracts.mdx) <Badge class="badge badge--secondary " text="object"/> 
The `Contracts` object provides a **paginated, cursor-based list** of smart contracts (`Contract` objects). It enables efficient traversal of large contract datasets while tracking pagination state via `cursor` and `remaining` fields.
### Example Query

```graphql
query Contracts($first: Int, $where: ContractFilter, $orderBy: ContractOrderBy, $orderDirection: OrderDirection) {
  contracts(first: $first, where: $where, orderBy: $orderBy, orderDirection: $orderDirection) {
    contracts {
      symbol
      name
      createdAt
      id
    }
  }
}
```

### Use Cases

* Contract discovery (browse all contracts on a given chain and filter by type).
* Analytics dashboards (track holder distribution and contract deployment).
* Multi-Contract UIs (display ranked lists on a marketplace frontend).

