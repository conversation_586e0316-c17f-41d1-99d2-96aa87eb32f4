---
title: OneSource Web3 API Reference
pagination_prev: null
---
import React from 'react';
import ZoomImage from '@site/src/components/ZoomImage';
import BrowserOnly from '@docusaurus/BrowserOnly';

<figure style={{textAlign: 'center'}}>
  <ZoomImage
    src={require('/img/Voyager.png')}
    alt="OneSource Web3 API GraphQL Voyager Schema Visualization"
  />
  <figcaption style={{fontStyle: 'italic', marginTop: '0.5rem'}}>
    OneSource Web3 API schema visualization in GraphQL Voyager
  </figcaption>
</figure>

<BrowserOnly>
  {() => {
    const { useEffect } = require('react');
    const useAnchorScroll = () => {
      useEffect(() => {
        const hash = window.location.hash;
        if (hash) {
          const element = document.getElementById(hash.substring(1));
          if (element) {
            setTimeout(() => {
              element.scrollIntoView();
            }, 100);
          }
        }
      }, []);
    };
    useAnchorScroll();
    return null;
  }}
</BrowserOnly>

## GraphQL Endpoints

The OneSource Web3 API receives requests at the following endpoint URLs:

* **Ethereum Mainnet**: [https://api.onesource.io/v1/ethereum/graphql](https://api.onesource.io/v1/ethereum/graphql)
* **Optimism**: [https://api.onesource.io/nftindexer/optimism/graphql](https://api.onesource.io/nftindexer/optimism/graphql)
* **Sepolia Testnet**: [https://api.onesource.io/nftindexer/sepolia/graphql](https://api.onesource.io/nftindexer/sepolia/graphql)

## Using the API Reference

When using the API Reference, it may be helpful to refer to the [GraphQL documentation](https://graphql.org/learn/) for information on GraphQL terms and concepts.

Defined below are terms as they relate to this API Reference.

### [Queries](./queries)

The root `Type` that is the entry point for all read operations.

| Term        | Description                                                                 |
|--------------|-----------------------------------------------------------------------------|
| **Arguments**    | Customizes `Query` behavior.                                 |
| **Types**        | Usually an `Object`, what constitutes a response to a `Query`. |

### [Types](./types)

`Types` define the shape and structure of the data received in response to a `Query`.

| Term            | Description                                                                                                                                         |
|-----------------|-----------------------------------------------------------------------------------------------------------------------------------------------------|
| **Fields**      | A unit of data that can be requested from a `Type`.                                                                               |
| **Implemented By** | Specifies which `Types` use this `Interface`.                                                                                                |
| **Member Of**   | The schema components of which this `Type` is a constituent. |
| **Returned By** | The `Types` that return this `Type` as a response.       |
| **Values**      | Predefined, named constants that represent the only valid options for a `Field` or `Argument`.                                   |
