---
pagination_prev: null
title: Getting Started
description: Making your first OneSource API call is only a few short steps away.
---
## [Subscription Plans](subscription-plans)

OneSource is available through free and paid tier subscription plans.

## [Get an API Key](get-an-api-key)

Subscribe through the AWS Marketplace to obtain an API key.

## [Authentication](authentication)

Making OneSource API calls requires proper authentication with an API key.

## [Playgrounds](playgrounds)

Create your first query using a GraphQL playground.
