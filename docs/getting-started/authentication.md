---
title: Authentication
---
To access the OneSource API, you need to include an API key in your requests. The API key is passed as an HTTP header.

## API Key Format

Your API key follows this format:

```plaintext
x-bp-token: BP-{KEYVALUE}
```

* `x-bp-token`: This is the header key used to pass the API key.
* `BP-{KEYVALUE}`: This is the API key value, where `{KEYVALUE}` is a unique 26 character alphanumeric string provided to you via your OneSource dashboard.

import React from 'react';
import ZoomImage from '@site/src/components/ZoomImage';

<ZoomImage
  src={require('/img/Authentication1.png')}
  alt="OneSource API Key Value"
/>

## How to Use the API Key

Include the API key in the headers of your HTTP requests. Here's an example using `curl`:

```bash
curl -X GET "https://api.onesource.io/nftindexer/mainnet/graphql" \
     -H "x-bp-token: BP-12345678912345678912345678"
```

Replace `BP-12345678912345678912345678` with your actual API key.

***

## Getting Your API Key

Refer to [Get an API Key](get-an-api-key) for information on how to obtain an API key.

***

## Best Practices

* **Keep your API key secure**: Do not share your API key publicly or commit it to version control systems like GitHub.
* **Rotate keys regularly**: For enhanced security, rotate your API keys periodically.
* **Use environment variables**: Store your API key in environment variables to avoid hardcoding it in your application.

***

## Troubleshooting

If you receive a `status code 401` or `Invalid API token` error, double-check that:

* The API key is correctly formatted (`x-bp-token: BP-{KEYVALUE}`).
* The API key is included in the requests header.
* The API key is valid and has not expired. You can check this via your OneSource dashboard.
