---
title: Example Queries
---

## Query Templates

Use these query templates to get acquainted with OneSource.

Please refer to the [OneSource Web3 API Reference](onesource-web3-api-reference/README.md) for comprehensive documentation of all available queries to use as guidance when building your own queries.

### Get ERC-20 Token Details

This query fetches all available data about a given ERC-20 token contract.

```graphql
query Contract($contractId: ID!) {
  contract(id: $contractId) {
    id
    type
    name
    symbol
    decimals
    createdAt
    createdBlock
    holders
  }
}
```

Variables

```json
{
  "contractId": "contract-address-here"
}
```

### Get NFT Token Details

This query fetches the name, description, medium thumbnail image, and contract address and type of a given NFT token.

```graphql
query Token($contract: ID!, $tokenID: ID!) {
  token(contract: $contract, tokenID: $tokenID) {
    name
    description
    image {
      thumbnails(where: { preset: MEDIUM }) {
        url
      }
    }
    contract {
      id
      isERC721
      isERC1155
    }
  }
}
```

Variables

```json
{
  "contract": "NFT-contract-address-here",
  "tokenID": "123"
}
```

### Get NFT Token Details With Image

This query fetches the name, symbol, and type of an NFT contract as well as its token URI and every available media thumbnail size with dimensions and type of each.

```graphql
query GetTokenWithImage(
  $contract: ID!
  $tokenID: ID!
) {
  token(
    contract: $contract
    tokenID: $tokenID
  ) {
    contract {
      id
      type
      name
      symbol
      decimals
    }
    tokenID
    tokenURI
    tokenURIStatus
    image {
      status
      url
      contentType
      width
      height
      thumbnails {
        preset
        status
        url
        width
        height
        contentType
        createdAt
      }
      createdAt
      errorMsg
    }
    createdAt
    createdBlock
  }
}
```

Variables

```json
{
"contract": "NFT-contract-address-here",
"tokenID": "123"
}
```

### Get Wallet Token Balance

This query fetches a wallet's balance of a given ERC-20 token contract as well as the ERC-20 token contract's name, symbol and type. This query could be amended for an ERC-721 or ERC-1155 token by adding the `tokenID` argument to the query and variables.

```graphql
query Balance($owner: ID!, $contract: ID!) {
  balance(owner: $owner, contract: $contract) {
    value
    contract {
      name
      symbol
    }
    contractType
    owner
  }
}
```

Variables

```json
{ "owner": "wallet-address-here",
  "contract": "erc-20-contract-address-here"
}
```

### Get All Tokens With Name Containing a Word

This query fetches all tokens with a name containing a word or string of characters.

```graphql
query Contracts($where: ContractFilter) {
  contracts(where: $where) {
    contracts {
      name
      symbol
      type
      id
    }
  }
}
```

Variables

```json
{
  "where": {
    "name_like": "word-here"
  }
}
```