---
title: GraphiQL Playground
sidebar_label: GraphiQL Playground
hide_table_of_contents: true
---

import GraphQLPlayground from '@site/src/components/GraphQLPlayground';
import ManualContent from './examples.mdx'
import BrowserOnly from '@docusaurus/BrowserOnly';

<BrowserOnly>
  {() => {
    const { useEffect } = require('react');
    const useAnchorScroll = () => {
      useEffect(() => {
        const hash = window.location.hash;
        if (hash) {
          const element = document.getElementById(hash.substring(1));
          if (element) {
            setTimeout(() => {
              element.scrollIntoView();
            }, 100);
          }
        }
      }, []);
    };
    useAnchorScroll();
    return null;
  }}
</BrowserOnly>

This embedded playground allows you to submit any query to the OneSource Web3 API Ethereum endpoint and get a real-time response without needing your own API key.

<GraphQLPlayground 
  height="900px"
  query={`query GetContractDetails($id: ID!) {
  contract(id: $id) {
    id
    name
    symbol
    isERC721
    isERC1155
    supportsMetadata
    createdAt
  }
}`}
variables={JSON.stringify({
  "id": "******************************************"
})}
/>

<ManualContent />
