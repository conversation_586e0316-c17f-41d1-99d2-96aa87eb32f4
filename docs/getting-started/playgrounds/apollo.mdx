---
title: Apollo Sandbox
---
import React, { useEffect } from 'react';
import BrowserOnly from '@docusaurus/BrowserOnly';
import ZoomImage from '@site/src/components/ZoomImage';

import ManualContent from './examples.mdx'

### Key Features

* **Interactive Query Builder**: Build queries by selecting arguments and fields from the schema documentation.
* **Comprehensive Documentation**: Auto-generated documentation for all types and queries including field descriptions.
* **Debugging Tools**: Pretty-printed JSON results with collapsible sections and detailed error messages with path highlighting.

### Accessing Apollo Sandbox

This [link](https://studio.apollographql.com/sandbox/explorer?endpoint=https%3A%2F%2Fapi.onesource.io%2Fv1%2Fethereum%2Fgraphql&headers=%7B%22x-bp-token%22%3A%22BP-%7BKEYVALUE%7D%22%7D) loads Apollo Sandbox pre-configured with the OneSource Web3 API Ethereum [endpoint](onesource-web3-api-reference/README.md#graphql-endpoints) and header key.

Simply replace `BP-{KEYVALUE}` in the **Headers** tab with your [API key value](/getting-started/authentication#api-key-format) (`BP-{KEYVALUE}` only) and you're ready to begin.

<ZoomImage
  src={require('/img/Playground7.png')}
  alt="Apollo Sandbox Playground"
/>

### Using Apollo Sandbox

Although the above link to Apollo Sandbox is not pre-configured with query templates like the OneSource Playground, it is quite easy to build your own queries with the Apollo Sandbox interactive query builder.

Below is how you would select arguments and fields to recreate the [Get ERC-20 Token Details](#get-erc-20-token-details) example query below.

<ZoomImage
  src={require('/img/Playground1.gif')}
  alt="Apollo Sandbox Playground Query Build"
/>

***

:::warning

It is important to note that Apollo Sandbox by default polls the connected endpoint for schema changes every 5 seconds. Each poll is counted by OneSource as a request against your API key. You may wish to disable this feature so as to conserve your requests, particularly if you intend to be working with the Apollo Sandbox for extended periods of time.

This setting can be disabled by navigating to the **settings** where you can edit the **Connection settings**. In the **Connection settings** menu, toggle the **Auto Update** feature to the **OFF** position as shown below.

:::

<ZoomImage
  src={require('/img/Playground9.png')}
  alt="Apollo Sandbox Playground Connection Settings"
/>

### Other Features

Apollo Sandbox allows you to retain and reference your history and browse the schema just like the OneSource Playground. You can access these features as shown below.

<ZoomImage
  src={require('/img/Playground10.png')}
  alt="Apollo Sandbox Playground Other Features"
/>

<ManualContent />
