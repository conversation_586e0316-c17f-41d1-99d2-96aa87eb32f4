---
title: Playgrounds
---

Playgrounds are interactive environments where you can test and explore our GraphQL API in real-time. Whether you just want to give OneSource Web3 API a try or you're experimenting with queries and debugging responses, playgrounds provide a user-friendly interface to help you get the most out of our API.

## [GraphiQL Playground](graphiql)

The embedded GraphiQL Playground allows you to explore the OneSource Web3 API Ethereum endpoint without leaving this documentation and without your own API key. This is a great option for those who simply want to give OneSource a try.

## [OneSource Playground](onesource)

The OneSource Playground is our in-house playground pre-programmed with several query templates ready to run. Those already familiar with GraphQL may appreciate this playground option.

## [Apollo Sandbox](apollo)

Apollo Sandbox provides a full-featured playground experience with an interactive query builder. This is the best option for those new to GraphQL and in need of assistance building queries.