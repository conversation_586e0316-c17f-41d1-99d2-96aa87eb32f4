---
title: OneSource Playground
---
import React, { useEffect } from 'react';
import BrowserOnly from '@docusaurus/BrowserOnly';
import ZoomImage from '@site/src/components/ZoomImage';

import ManualContent from './examples.mdx'

## Key Features

* **Autocomplete & Validation**: Get suggestions and error messages as you type, ensuring your requests are correct.
* **Query Templates:** The playground is preloaded with a selection of query templates to get you started right away.
* **Pre-configured for OneSource**: Our Ethereum endpoint is pre-configured so that you can simply input your API key upon page load and start playing.

## Accessing the Playground

To access the OneSource Playground:

1. [Login to your OneSource account](/getting-started/get-an-api-key#accessing-your-onesource-dashboard-after-registration) and copy your API key value (`BP-{KEYVALUE}` only).
2. Navigate your browser to [playground.onesource.io](https://playground.onesource.io/).
3. Paste your API key value into the prompt that appears upon loading the playground and push 'OK'.

<ZoomImage
  src={require('/img/Playground1.png')}
  alt="OneSource Playground Enter API Key"
/>

:::note

You must have an active API key to use the playground. If you don't have one, refer to [Get an API Key](/getting-started/get-an-api-key) for information on how to get one.

:::

Should you make a mistake when pasting your API key value into the prompt, navigate to the playground settings via the gear wheel icon in the upper right and correct your API key value.

<ZoomImage
  src={require('/img/Playground8.png')}
  alt="OneSource Playground Settings"
/>

## Using the Playground

### Execute the Preloaded Queries

The playground is preloaded with three query templates with query variables ready to execute:

1. **Get Token**: Retrieves data pertaining to a particular ERC-721 token.
2. **Get Token with Image**: Retrieves data and images pertaining to a particular ERC-721 token.
3. **Get Owner by Balances by Contract**: Retrieves balances data pertaining to a particular ERC-721 token for a specific wallet address.

Get started with the playground by executing the preloaded query templates with query variables using the **play** button in the middle of the interface and evaluating the responses that appear in the right panel.

<ZoomImage
  src={require('/img/Playground2.png')}
  alt="OneSource Playground Customize Preloaded Query"
/>

### Customize Preloaded Query Variables

Try replacing the query variables with variables more relevant to your interests or project and executing the queries again.

<ZoomImage
  src={require('/img/Playground3.png')}
  alt="OneSource Playground Preloaded Query"
/>

### Write a Custom Query

To begin writing a custom query, click the **+** button to open a new tab.

<ZoomImage
  src={require('/img/Playground4.png')}
  alt="OneSource Playground Custom Query"
/>

For guidance on how to write and structure a custom query, refer to the [OneSource Web3 API Reference](onesource-web3-api-reference/README.md). You may also refer to the **DOCS** and **SCHEMA** tabs on the right side of the playground for reference when writing custom queries.

<ZoomImage
  src={require('/img/Playground5.png')}
  alt="OneSource Playground Docs/Schema Tabs"
/>

## History

The playground saves every query that you run throughout your session. Click the **HISTORY** button to view and revert back to previously executed queries from your current session.

<ZoomImage
  src={require('/img/Playground6.png')}
  alt="OneSource Playground History"
/>

You can **STAR** specific queries to make them easier to find in your session history.

<ManualContent />

