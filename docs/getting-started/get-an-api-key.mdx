---
title: Get an API Key
---
To get started with OneSource, you must first obtain an API key via [OneSource Web3 API on AWS Marketplace](https://aws.amazon.com/marketplace/pp/prodview-at5xou6obw4s4).

## Step 1: Create or Sign In to an AWS Account

import React, { useEffect } from 'react';
import BrowserOnly from '@docusaurus/BrowserOnly';
import ZoomImage from '@site/src/components/ZoomImage';

<BrowserOnly>
  {() => {
    const { useEffect } = require('react');
    const useAnchorScroll = () => {
      useEffect(() => {
        const hash = window.location.hash;
        if (hash) {
          const element = document.getElementById(hash.substring(1));
          if (element) {
            setTimeout(() => {
              element.scrollIntoView();
            }, 100);
          }
        }
      }, []);
    };
    useAnchorScroll();
    return null;
  }}
</BrowserOnly>

<ZoomImage
  src={require('/img/GetAnAPIKey1.png')}
  alt="AWS Marketplace Sign In or Create Account"
/>

If you do not yet have an AWS account, you will need to [create one](https://signin.aws.amazon.com/signup?request_type=register). If you already have an AWS account, you will need to [sign in](https://aws.amazon.com/marketplace/login).

## Step 2: Review Subscription Options and Confirm Subscription

Once you've signed into your AWS account, return to [OneSource Web3 API on AWS Marketplace](https://aws.amazon.com/marketplace/pp/prodview-at5xou6obw4s4) and click the **View purchase options** button.

<ZoomImage
  src={require('/img/GetAnAPIKey2.png')}
  alt="AWS Marketplace Confirm Subscription"
/>

Select your auto-renewal preference and then scroll down to 'Pricing details and unit configuration'. Select your desired subscription tier and then click the **Subscribe** button to finalize your subscription. Refer to the [Subscription Plans](subscription-plans) page for more information on subscriptions.

## Step 3: Set up OneSource Account

<ZoomImage
  src={require('/img/GetAnAPIKey3.png')}
  alt="AWS Marketplace Subscription Confirmed"
/>

It will take a couple of minutes for AWS Marketplace to complete your subscription. When your subscription is complete, click the **Set up your account** button to be taken to the OneSource website to set up your account and obtain your API key.

<ZoomImage
  src={require('/img/signup.png')}
  alt="OneSource Account Registration"
/>

Enter your email address, check the boxes consenting to future communications from OneSource (you can unsubscribe later) and acknowledging that you are 18 years of age or older, complete the Captcha, and click **Get Started**.

<ZoomImage
  src={require('/img/GetAnAPIKey5.png')}
  alt="OneSource Confirmation Email"
/>

An email will be sent to the email address you provided with a link you can use to access your OneSource account and API key.

## Step 4: Login to OneSource Account to Get Your API Key

Clicking the link in the confirmation email will take you to your OneSource dashboard where you can view your subscription details, API key and current cycle utilization.

<ZoomImage
  src={require('/img/GetAnAPIKey6.png')}
  alt="OneSource Dashboard"
/>

If you require additional API keys to be used under your subscription, you can generate additional API keys with the **Create new API key +** button.
