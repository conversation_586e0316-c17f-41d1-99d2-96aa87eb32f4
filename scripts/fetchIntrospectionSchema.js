/**
 * This script fetches the full GraphQL schema via introspection
 * and saves it as a JSON file (introspectionSchema.json).
 *
 * Purpose:
 * - Informational only — helps to explore the entire schema structure.
 * - Does NOT affect the actual documentation generation pipeline.
 *
 * Useful for debugging, development, and understanding schema details.
 * 
 * To run:
 *   node scripts/fetchIntrospectionSchema.js
 */

const fetch = require("node-fetch");
const fs = require("fs/promises");
require("dotenv").config();

const TARGET_URL = "https://api.onesource.io/v1/ethereum/graphql";
const API_TOKEN = process.env.ONESOURCE_API_TOKEN || "";
const CACHE_PATH = ".cache/introspectionSchema.json"; // Path to save schema

// Full GraphQL introspection query
const INTROSPECTION_QUERY = `
  query {
    __schema {
      queryType { name }
      mutationType { name }
      subscriptionType { name }
      types { ...FullType }
      directives {
        name
        description
        locations
        args { ...InputValue }
      }
    }
  }

  fragment FullType on __Type {
    kind
    name
    description
    fields(includeDeprecated: true) {
      name
      description
      args { ...InputValue }
      type { ...TypeRef }
      isDeprecated
      deprecationReason
    }
    inputFields { ...InputValue }
    interfaces { ...TypeRef }
    enumValues(includeDeprecated: true) {
      name
      description
      isDeprecated
      deprecationReason
    }
    possibleTypes { ...TypeRef }
  }

  fragment InputValue on __InputValue {
    name
    description
    type { ...TypeRef }
    defaultValue
  }

  fragment TypeRef on __Type {
    kind
    name
    ofType {
      kind
      name
      ofType {
        kind
        name
        ofType {
          kind
          name
        }
      }
    }
  }
`;

// Fetch schema and save it to file
async function fetchSchema() {
  // Create an AbortController to implement timeout
  const controller = new AbortController();
  // Set a 30-second timeout to abort the request if it takes too long
  const timeout = setTimeout(() => controller.abort(), 30_000);

  try {
    const res = await fetch(TARGET_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-bp-token": API_TOKEN,
      },
      body: JSON.stringify({ query: INTROSPECTION_QUERY }),
      signal: controller.signal,
    });

    // Clear timeout once response is received
    clearTimeout(timeout);

    const { data, errors } = await res.json();

    if (errors) {
      throw new Error(`Failed to fetch schema: ${JSON.stringify(errors)}`);
    }

    // Validate schema data exists and has the expected structure
    if (!data || !data.__schema) {
      throw new Error("Invalid schema data received from GraphQL endpoint");
    }

    if (!Array.isArray(data.__schema.types)) {
      throw new Error("Schema types missing or invalid");
    }

    // Save the fetched schema to a local file with pretty formatting
    await fs.writeFile(CACHE_PATH, JSON.stringify(data.__schema, null, 2), "utf8");
    console.log(`✅ Schema saved to ${CACHE_PATH}`);

    // Return the fetched schema for further processing
    return data.__schema;
  } catch (err) {
    // Clear timeout if an error occurs
    clearTimeout(timeout);

    if (err.name === "AbortError") {
      console.error("Schema fetch timed out after 30 seconds.");
    } else {
      console.error("Failed to fetch schema:", err);
    }
    process.exit(1);
  }
}

fetchSchema();
