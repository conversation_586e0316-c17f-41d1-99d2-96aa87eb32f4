// scripts/rewriteLinks.js
const { globSync } = require('glob');
const fs = require('fs');
const path = require('path');

const DOCS_DIR = path.join(__dirname, '../docs/onesource-web3-api-reference/**/*.mdx');

function rewriteSchemaLinks(content) {
  return content
    .replace(
      /\/schema\/types\/objects/g,
      '/onesource-web3-api-reference/types/objects'
    )
    .replace(
      /\/schema\/operations\/queries/g,
      '/onesource-web3-api-reference/queries'
    )
    .replace(
      /\/schema\/types\/scalars/g,
      '/onesource-web3-api-reference/types/scalars'
    );
}

// Process files
const files = globSync(DOCS_DIR, { windowsPathsNoEscape: true });
files.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    const rewrittenContent = rewriteSchemaLinks(content);
    
    if (rewrittenContent !== content) {
      fs.writeFileSync(file, rewrittenContent);
      console.log(`✅ Updated ${path.relative(process.cwd(), file)}`);
      
      // Debug: Show actual changes made
      const originalLinks = content.match(/\/schema\/types\/(objects|scalars|operations\/queries)\/[^)\s]+/g);
      const newLinks = rewrittenContent.match(/\/onesource-web3-api-reference\/types\/(objects|scalars|queries)\/[^)\s]+/g);
      if (originalLinks && newLinks) {
        console.log('Changed links:', { originalLinks, newLinks });
      }
    }
  } catch (error) {
    console.error(`❌ Error processing ${file}:`, error.message);
  }
});

console.log(`\n🔍 Processed ${files.length} files`);