const fs = require("fs");
const path = require("path");

// Capitalize the first letter of the "title" field in the auto-generated file
function capitalizeTitle(filePath) {
  const content = fs.readFileSync(filePath, "utf8");

  // Find the 'title: x' line and replace the first letter with uppercase
  const updated = content.replace(
    /title:\s*([a-z])/,
    (match, p1) => `title: ${p1.toUpperCase()}`
  );

  // Save the updated content back to the file
  fs.writeFileSync(filePath, updated, "utf8");
}

// Go through all .mdx files in the queries folder
const docsDir = "./docs/onesource-web3-api-reference/operations/queries";
fs.readdirSync(docsDir).forEach((file) => {
  const fullPath = path.join(docsDir, file);

  // Only process .mdx files
  if (file.endsWith(".mdx")) {
    capitalizeTitle(fullPath);
  }
});
