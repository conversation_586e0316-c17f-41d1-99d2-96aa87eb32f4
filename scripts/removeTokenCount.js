const fs = require('fs');
const path = require('path');

// Target pattern to remove (matches tokenCount reference and optional Bullet)
const TOKENCOUNT_PATTERN = /\[`tokenCount`\]\([^)]*token-count\.mdx\)\s*<Badge class="badge badge--secondary badge--relation" text="query"\/>\s*(?:<Bullet \/>)?/g;

function cleanMdxFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // First, remove all tokenCount references
        if (TOKENCOUNT_PATTERN.test(content)) {
            content = content.replace(TOKENCOUNT_PATTERN, '');
            modified = true;
        }

        // Then clean up any empty sections that may have been created
        const sections = content.split(/(^### .*$)/gm);
        let newContent = [];
        let skipNext = false;

        for (let i = 0; i < sections.length; i++) {
            if (skipNext) {
                skipNext = false;
                continue;
            }

            // Check if this is a section header
            if (sections[i].match(/^### .*$/)) {
                const nextSection = sections[i+1] || '';
                // If the section content is empty (just whitespace), skip both header and content
                if (nextSection.trim() === '') {
                    skipNext = true;
                    modified = true;
                    continue;
                }
                newContent.push(sections[i]);
            } else if (!skipNext) {
                newContent.push(sections[i]);
            }
        }

        content = newContent.join('');

        // Clean up any resulting multiple newlines
        content = content.replace(/\n{3,}/g, '\n\n');

        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Updated: ${filePath}`);
        } else {
            console.log(`No changes needed: ${filePath}`);
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}

function processDirectory(directory) {
    const files = fs.readdirSync(directory);
    
    files.forEach(file => {
        const fullPath = path.join(directory, file);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            processDirectory(fullPath);
        } else if (file.endsWith('.mdx')) {
            cleanMdxFile(fullPath);
        }
    });
}

// Main execution
const docsSchemaPath = path.join(process.cwd(), 'docs', 'onesource-web3-api-reference');
if (fs.existsSync(docsSchemaPath)) {
    console.log(`Processing files in ${docsSchemaPath}...`);
    processDirectory(docsSchemaPath);
    console.log('Processing complete!');
} else {
    console.error(`Directory not found: ${docsSchemaPath}`);
    console.error('Please run this script from the root of your project where the /docs/onesource-web3-api-reference directory exists.');
}