const fs = require('fs');
const path = require('path');

// Types to remove with their exact display names
const TYPES_TO_REMOVE = [
    'MetadataSyncInfo',
    'ChainSyncInfo',
    'SubStreamChainSyncInfo',
    'SwapFilter'
];

function cleanMdxFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Create regex pattern for each type to remove
        const patterns = TYPES_TO_REMOVE.map(type => 
            new RegExp(`\\[\\\`${type}\\\`\\]\\([^)]+\\)\\s*<Badge[^>]+\\/>\\s*(?:<Bullet \\/>)?`, 'g')
        );
        
        let modified = false;
        
        // Process each pattern
        patterns.forEach(pattern => {
            if (pattern.test(content)) {
                content = content.replace(pattern, '');
                modified = true;
            }
        });
        
        // Clean up any leftover empty lines or bullet points
        content = content.replace(/\n{3,}/g, '\n\n');
        
        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Updated: ${filePath}`);
        } else {
            console.log(`No changes needed: ${filePath}`);
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}

function processDirectory(directory) {
    const files = fs.readdirSync(directory);
    
    files.forEach(file => {
        const fullPath = path.join(directory, file);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            processDirectory(fullPath);
        } else if (file.endsWith('.mdx')) {
            cleanMdxFile(fullPath);
        }
    });
}

// Main execution
const docsSchemaPath = path.join(process.cwd(), 'docs', 'onesource-web3-api-reference');
if (fs.existsSync(docsSchemaPath)) {
    console.log(`Processing files in ${docsSchemaPath}...`);
    processDirectory(docsSchemaPath);
    console.log('Processing complete!');
} else {
    console.error(`Directory not found: ${docsSchemaPath}`);
    console.error('Please run this script from the root of your project where the /docs/onesource-web3-api-reference directory exists.');
}