const { spawn } = require("child_process");
const fetch = require("node-fetch");

const MIDDLEWARE_URL = "http://localhost:4000/"; // URL to check if middleware is ready
const MIDDLEWARE_START_COMMAND = "node";
const MIDDLEWARE_START_ARGS = ["middlewareServer.js"]; // path to middleware server file
const DOCS_GENERATE_COMMAND = "npm";
const DOCS_GENERATE_ARGS = ["run", "generate-docs"]; // command to generate docs (docusaurus graphql-to-doc)

// Time settings
const CHECK_INTERVAL_MS = 500; // how often to check if middleware is ready (in ms)
const STARTUP_TIMEOUT_MS = 15000; // max wait time for middleware startup (in ms)

// Helper function to wait some milliseconds
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Wait for middleware server to be ready by repeatedly checking the health endpoint ("/")
async function waitForMiddlewareReady(url, timeoutMs) {
  const start = Date.now();
  while (true) {
    try {
      const res = await fetch(url);
      if (res.ok) return true; // ready
    } catch {
      // if fetch fails, middleware not ready yet
    }

    // Check if we waited too long
    if (Date.now() - start > timeoutMs) {
      throw new Error("Timeout waiting for middleware to be ready");
    }

    // Wait for a short interval before retrying
    await delay(CHECK_INTERVAL_MS);
  }
}

// Main function to run middleware and docusaurus docs generation
async function run() {
  console.log("Starting middleware server...");
  const middlewareProcess = spawn(MIDDLEWARE_START_COMMAND, MIDDLEWARE_START_ARGS, {
    stdio: "inherit", // show middleware output in this terminal
  });

  try {
    // Wait until middleware is ready
    await waitForMiddlewareReady(MIDDLEWARE_URL, STARTUP_TIMEOUT_MS);
    console.log("Middleware server is ready.");

    console.log("Starting documentation generation...");
    const docsProcess = spawn(DOCS_GENERATE_COMMAND, DOCS_GENERATE_ARGS, {
      stdio: "inherit", // show docs generation output
      shell: true, // run in shell for npm scripts
    });

    // Wait until docs generation finishes
    await new Promise((resolve, reject) => {
      docsProcess.on("close", (code) => {
        if (code === 0) resolve();
        else reject(new Error(`Docs generation exited with code ${code}`));
      });
    });

    console.log("Documentation generation finished.");

  } catch (err) {
    console.error(err);
    process.exit(1); // exit with error code
  } finally {
    console.log("Stopping middleware server...");
    middlewareProcess.kill(); // stop middleware process
  }
}

// Run the main function
run();
