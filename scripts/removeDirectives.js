// scripts/removeDirectiveRefs.js
const { globSync } = require('glob');
const fs = require('fs');
const path = require('path');

const DOCS_DIR = path.join(__dirname, '../docs/onesource-web3-api-reference/**/*.mdx');

function cleanMemberOfSection(content) {
  return content.replace(
    /(### Member Of\r?\n\r?\n)([\s\S]*?)(\r?\n\r?\n|$)/g,
    (match, header, members, footer) => {
      // Split by `<Bullet />` to isolate each segment
      const segments = members.split(/<Bullet \/>/);
      const filteredSegments = segments.filter(segment => 
        !segment.includes('text="directive"')
      );
      
      if (filteredSegments.length === 0) {
        return ''; // Remove section if no segments remain
      }
      return `${header}${filteredSegments.join('<Bullet />')}${footer}`;
    }
  );
}

// Process files
const files = globSync(DOCS_DIR, { windowsPathsNoEscape: true });
files.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    const cleanedContent = cleanMemberOfSection(content);
    if (cleanedContent !== content) {
      fs.writeFileSync(file, cleanedContent);
      console.log(`✅ Updated ${file}`);
    } else {
      console.log(`⏩ No changes needed for ${file}`);
    }
  } catch (error) {
    console.error(`❌ Error with ${file}:`, error.message);
  }
});

console.log(`\n📊 Total processed: ${files.length} files`);