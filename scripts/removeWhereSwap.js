const fs = require('fs');
const path = require('path');

// Target pattern to remove (matches any line containing a link to swap-filter.mdx)
const TARGET_PATTERN = /^.*\/swap-filter\.mdx\).*$/gm;

function cleanMdxFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Check if content contains the target pattern
        if (TARGET_PATTERN.test(content)) {
            // Remove all lines matching the pattern
            content = content.replace(TARGET_PATTERN, '');
            modified = true;

            // Clean up any resulting double newlines
            content = content.replace(/\n{3,}/g, '\n\n');
        }

        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Updated: ${filePath}`);
        } else {
            console.log(`No changes needed: ${filePath}`);
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}

function processDirectory(directory) {
    const files = fs.readdirSync(directory);
    
    files.forEach(file => {
        const fullPath = path.join(directory, file);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            processDirectory(fullPath);
        } else if (file.endsWith('.mdx')) {
            cleanMdxFile(fullPath);
        }
    });
}

// Main execution
const docsSchemaPath = path.join(process.cwd(), 'docs', 'onesource-web3-api-reference');
if (fs.existsSync(docsSchemaPath)) {
    console.log(`Processing files in ${docsSchemaPath}...`);
    processDirectory(docsSchemaPath);
    console.log('Processing complete!');
} else {
    console.error(`Directory not found: ${docsSchemaPath}`);
    console.error('Please run this script from the root of your project where the /docs/onesource-web3-api-reference directory exists.');
}