// scripts/cleanGeneratedFiles.js
const fs = require('fs');
const path = require('path');
const { globSync } = require('glob');

// Files and directories to delete
const TARGETS = [
  // Directories to completely remove
  '/docs/onesource-web3-api-reference/operations/directives',
  '/docs/onesource-web3-api-reference/types/directives',

  // Specific files to delete
  '/docs/onesource-web3-api-reference/operations/queries/chain-sync-info.mdx',
  '/docs/onesource-web3-api-reference/operations/queries/metadata-sync-info.mdx',
  '/docs/onesource-web3-api-reference/operations/queries/token-count.mdx',
  
  '/docs/onesource-web3-api-reference/types/enums/order-side.mdx',
  '/docs/onesource-web3-api-reference/types/enums/swap-status.mdx',
  '/docs/onesource-web3-api-reference/types/enums/sync-status.mdx',
  
  '/docs/onesource-web3-api-reference/types/inputs/swap-filter.mdx',
  
  '/docs/onesource-web3-api-reference/types/objects/chain-sync-info.mdx',
  '/docs/onesource-web3-api-reference/types/objects/metadata-sync-info.mdx',
  '/docs/onesource-web3-api-reference/types/objects/sub-stream-chain-sync-info.mdx',
  
  // Scalar files to delete
  '/docs/onesource-web3-api-reference/types/scalars/any.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/float.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/int-64.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/int-256.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/map.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/uint-8.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/uint-32.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/uint-256.mdx'
];

// Process all targets
TARGETS.forEach(target => {
  const fullPath = path.join(__dirname, '..', target);
  
  try {
    if (fs.existsSync(fullPath)) {
      if (fs.lstatSync(fullPath).isDirectory()) {
        // Delete directory recursively
        fs.rmSync(fullPath, { recursive: true, force: true });
        console.log(`🗑️  Deleted directory: ${target}`);
      } else {
        // Delete single file
        fs.unlinkSync(fullPath);
        console.log(`🗑️  Deleted file: ${target}`);
      }
    } else {
      console.log(`⏩ Not found: ${target} (skipping)`);
    }
  } catch (error) {
    console.error(`❌ Failed to delete ${target}:`, error.message);
  }
});

// Verify scalar deletions by pattern (in case of case sensitivity issues)
const SCALAR_PATTERNS = [
  '/docs/onesource-web3-api-reference/types/scalars/{a,A}ny.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/{f,F}loat.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/int-{64,256}.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/{m,M}ap.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/unit-8.mdx',
  '/docs/onesource-web3-api-reference/types/scalars/uint-{32,256}.mdx'
];

SCALAR_PATTERNS.forEach(pattern => {
  const files = globSync(path.join(__dirname, '..', pattern));
  files.forEach(file => {
    try {
      fs.unlinkSync(file);
      console.log(`🗑️  Deleted matched scalar: ${path.relative(path.join(__dirname, '..'), file)}`);
    } catch (error) {
      console.error(`❌ Failed to delete scalar file ${file}:`, error.message);
    }
  });
});

console.log('\n🧹 Cleanup complete!');