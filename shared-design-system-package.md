# Shared Design System Package

## Overview

This document outlines the components and structure for our private npm package that will serve as a shared design system between the 1s-developer-docs (Docusaurus) and site-next-frontend (Next.js) projects.

## Package Structure

```
@blockparty/design-system/
├── src/
│   ├── tokens/
│   │   ├── colors.ts
│   │   ├── typography.ts
│   │   ├── spacing.ts
│   │   └── index.ts
│   ├── components/
│   │   ├── atoms/
│   │   ├── molecules/
│   │   └── organisms/
│   ├── adapters/
│   │   ├── docusaurus/
│   │   └── chakra/
│   └── utils/
├── dist/
├── package.json
└── README.md
```

---

## 🎨 Design Tokens

### Colors (`tokens/colors.ts`)
```typescript
export const colors = {
  // Brand Colors
  primary: '#19DFAE',
  primaryDark: '#071F21',
  primaryLight: '#5FF6CC',
  
  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#F9F5F1',
    100: '#F2EAE2',
    200: '#E5D3C3',
    300: '#D1A8D5',
    400: '#9B9BA1',
    500: '#311D19',
    600: '#2D1901',
    700: '#071F21',
    800: '#003235',
    900: '#000000'
  },
  
  // Semantic Colors
  success: '#19DFAE',
  warning: '#EF992F',
  error: '#C63131',
  info: '#19BADF',
  
  // Alpha Variants
  alpha: {
    primary10: 'rgba(25, 223, 174, 0.1)',
    primary25: 'rgba(25, 223, 174, 0.25)',
    primary50: 'rgba(25, 223, 174, 0.5)',
    dark25: 'rgba(7, 31, 33, 0.25)',
    dark50: 'rgba(7, 31, 33, 0.5)',
    dark90: 'rgba(7, 31, 33, 0.9)'
  }
}
```

### Typography (`tokens/typography.ts`)
```typescript
export const typography = {
  fonts: {
    body: 'Figtree, ui-sans-serif, system-ui, sans-serif',
    heading: 'Mabry Medium Pro, Figtree, sans-serif',
    mono: 'MonaspaceNeon, Neon-Regular, Consolas, monospace'
  },
  
  fontSizes: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    md: '1rem',       // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem'  // 60px
  },
  
  fontWeights: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  },
  
  lineHeights: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75
  }
}
```

### Spacing (`tokens/spacing.ts`)
```typescript
export const spacing = {
  0: '0px',
  1: '4px',
  2: '8px',
  3: '12px',
  4: '16px',
  5: '20px',
  6: '24px',
  8: '32px',
  10: '40px',
  12: '48px',
  16: '64px',
  20: '80px',
  24: '96px',
  32: '128px',
  40: '160px',
  48: '192px',
  56: '224px',
  64: '256px'
}

export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}
```

---

## 🧩 Components

### Atoms

#### Button (`components/atoms/Button/`)
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost' | 'icon';
  size: 'sm' | 'md' | 'lg';
  theme?: 'light' | 'dark';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}
```

**Variants:**
- `primary` - Main action button (green background)
- `secondary` - Secondary action (outlined)
- `ghost` - Minimal styling (text only)
- `icon` - Square button for icons

#### Input (`components/atoms/Input/`)
```typescript
interface InputProps {
  variant: 'default' | 'contact' | 'email';
  theme?: 'light' | 'dark';
  label?: string;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
}
```

#### Typography (`components/atoms/Typography/`)
```typescript
interface TypographyProps {
  variant: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'mono';
  color?: string;
  align?: 'left' | 'center' | 'right';
  children: React.ReactNode;
}
```

#### Checkbox (`components/atoms/Checkbox/`)
```typescript
interface CheckboxProps {
  label: string;
  checked?: boolean;
  disabled?: boolean;
  theme?: 'light' | 'dark';
  onChange: (checked: boolean) => void;
}
```

#### Link (`components/atoms/Link/`)
```typescript
interface LinkProps {
  href: string;
  variant: 'default' | 'button' | 'nav';
  external?: boolean;
  children: React.ReactNode;
}
```

### Molecules

#### Card (`components/molecules/Card/`)
```typescript
interface CardProps {
  variant: 'default' | 'feature' | 'post' | 'grid';
  title?: string;
  description?: string;
  image?: string;
  tags?: string[];
  children?: React.ReactNode;
}
```

#### FormField (`components/molecules/FormField/`)
```typescript
interface FormFieldProps {
  label: string;
  required?: boolean;
  error?: string;
  helpText?: string;
  children: React.ReactNode;
}
```

#### SearchBox (`components/molecules/SearchBox/`)
```typescript
interface SearchBoxProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  theme?: 'light' | 'dark';
}
```

#### Tag (`components/molecules/Tag/`)
```typescript
interface TagProps {
  label: string;
  variant: 'default' | 'category' | 'status';
  color?: string;
  removable?: boolean;
  onRemove?: () => void;
}
```

### Organisms

#### Navigation (`components/organisms/Navigation/`)
```typescript
interface NavigationProps {
  items: NavigationItem[];
  theme?: 'light' | 'dark';
  mobile?: boolean;
  logo?: React.ReactNode;
}

interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
  external?: boolean;
}
```

#### Header (`components/organisms/Header/`)
```typescript
interface HeaderProps {
  navigation: NavigationItem[];
  theme?: 'light' | 'dark';
  logo?: React.ReactNode;
  actions?: React.ReactNode;
}
```

#### Footer (`components/organisms/Footer/`)
```typescript
interface FooterProps {
  links: FooterSection[];
  copyright?: string;
  theme?: 'light' | 'dark';
}

interface FooterSection {
  title: string;
  links: { label: string; href: string; external?: boolean }[];
}
```

#### ContactForm (`components/organisms/ContactForm/`)
```typescript
interface ContactFormProps {
  onSubmit: (data: ContactFormData) => void;
  theme?: 'light' | 'dark';
  fields?: FormFieldConfig[];
}
```

---

## 🔧 Adapters

### Docusaurus Adapter (`adapters/docusaurus/`)
- CSS variable mappings
- Infima theme integration
- Component wrappers for Docusaurus compatibility

### Chakra UI Adapter (`adapters/chakra/`)
- Chakra theme object generation
- Component style mappings
- Hook integrations

---

## 📦 Package Configuration

### `package.json`
```json
{
  "name": "@blockparty/design-system",
  "version": "1.0.0",
  "private": true,
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "exports": {
    ".": "./dist/index.js",
    "./tokens": "./dist/tokens/index.js",
    "./components": "./dist/components/index.js",
    "./adapters/docusaurus": "./dist/adapters/docusaurus/index.js",
    "./adapters/chakra": "./dist/adapters/chakra/index.js"
  },
  "peerDependencies": {
    "react": ">=17.0.0",
    "react-dom": ">=17.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "typescript": "^5.0.0",
    "rollup": "^3.0.0"
  }
}
```

---

## 🚀 Usage Examples

### In Docusaurus Project
```typescript
import { Button, colors } from '@blockparty/design-system';
import { DocusaurusAdapter } from '@blockparty/design-system/adapters/docusaurus';

// Apply theme
DocusaurusAdapter.applyTheme();

// Use components
<Button variant="primary" size="md">
  Get Started
</Button>
```

### In Next.js/Chakra Project
```typescript
import { Button, typography } from '@blockparty/design-system';
import { ChakraAdapter } from '@blockparty/design-system/adapters/chakra';

// Create Chakra theme
const theme = ChakraAdapter.createTheme();

// Use components
<Button variant="primary" size="md">
  Get Started
</Button>
```

---

## 📋 Implementation Phases

### Phase 1: Foundation (Week 1-2)
- [ ] Design tokens (colors, typography, spacing)
- [ ] Package structure setup
- [ ] Build configuration

### Phase 2: Atoms (Week 3-4)
- [ ] Button component
- [ ] Input component
- [ ] Typography component
- [ ] Link component
- [ ] Checkbox component

### Phase 3: Molecules (Week 5-6)
- [ ] Card component
- [ ] FormField component
- [ ] Tag component
- [ ] SearchBox component

### Phase 4: Organisms (Week 7-8)
- [ ] Navigation component
- [ ] Header component
- [ ] Footer component
- [ ] ContactForm component

### Phase 5: Adapters (Week 9-10)
- [ ] Docusaurus adapter
- [ ] Chakra UI adapter
- [ ] Integration testing

### Phase 6: Documentation & Release (Week 11-12)
- [ ] Storybook documentation
- [ ] Usage examples
- [ ] Migration guides
- [ ] Package publishing

---

## 🎯 Success Criteria

- [ ] All components work in both Docusaurus and Next.js projects
- [ ] Design consistency across both applications
- [ ] Reduced development time for new features
- [ ] Easy maintenance and updates
- [ ] Comprehensive documentation and examples

This package will serve as the foundation for consistent design across all Blockparty projects while maintaining flexibility for platform-specific requirements.
