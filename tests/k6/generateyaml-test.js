import http from "k6/http";
import { check, sleep } from "k6";
import { Trend } from "k6/metrics";

// Custom metric to track generation duration
export const generationDuration = new Trend("generation_duration");

export let options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  sleep(1); // Wait 1 second to ensure the middleware server has started

  const res = http.get("http://localhost:4000/generate");
  const body = res.json();

  console.log(`📏 Generation took ${body.duration}ms`);

  // Record custom generation duration metric
  generationDuration.add(body.duration);

  // Validate response
  check(res, {
    "status is 200": (r) => r.status === 200,
    "generation completed": () => body.message === "Generation complete",
    "generation under 1s": () => body.duration < 1000,
  });
}
