# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.editorconfig

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.staging
.env.production
.npmrc

# Ignore changes to the temp token file after it's been added
/public/wp-token.json
!/public/wp-token.json

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
tmp/wp-token.json
