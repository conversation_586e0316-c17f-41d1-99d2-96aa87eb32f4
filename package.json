{"name": "blockparty-lp", "version": "0.1.0", "private": true, "type": "module", "scripts": {"generate-token": "node --loader ts-node/esm generate-token/index.ts", "dev": "npm run generate-token && next dev", "build": "npm run generate-token && next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --quiet --fix", "codegen": "graphql-codegen --config --debug --verbose codegen.ts", "build:theme": "babel src --extensions \".ts,.tsx\" --out-dir extract-css --copy-files --config-file ./babel.config.scripts.js", "start:server": "node extract-css/extractCSS.mjs", "lint:css": "prettier --write exported.css", "open:page": "open-cli http://localhost:3000", "wait-for-server": "wait-on http://localhost:3000 && npm run open:page", "extract:css:with-open": "concurrently \"npm run start:server\" \"npm run wait-for-server\"", "build-and-extract-css": "npm-run-all build:theme extract:css:with-open lint:css", "prepare": "husky"}, "dependencies": {"@apollo/client": "^3.10.8", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.2.0", "@chakra-ui/react": "^2.8.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.0.9", "@graphql-codegen/typescript-operations": "^4.2.3", "@graphql-codegen/typescript-react-apollo": "^4.3.0", "@vercel/analytics": "^1.5.0", "css": "^3.0.0", "dotenv": "^16.5.0", "entities": "^6.0.0", "eslint-plugin-react": "^7.37.5", "framer-motion": "^11.3.7", "graphql": "^16.9.0", "highlight.js": "^11.11.1", "lottie-react": "^2.4.1", "next": "^14.2.23", "node-html-parser": "^6.1.13", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-json-pretty": "^2.2.0", "react-swipeable": "^7.0.2", "sharp": "^0.34.1", "shiki": "^3.3.0", "ts-node": "^10.9.2", "two.js": "^0.8.15"}, "devDependencies": {"@babel/cli": "^7.25.6", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@types/he": "^1.2.3", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "babel-loader": "^9.1.3", "babel-plugin-add-import-extension": "^1.6.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-module-resolver": "^5.0.2", "concurrently": "^9.0.1", "eslint": "^8", "eslint-config-next": "14.2.5", "he": "^1.2.0", "husky": "^9.1.7", "npm-run-all": "^4.1.5", "open-cli": "^8.0.0", "prettier": "^3.5.3", "prettier-plugin-sh": "^0.17.2", "ts-node": "^10.9.2", "typescript": "^5.7.3", "wait-on": "^8.0.1"}}