{"name": "onesourcedocs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "postgenerate-docs": "node scripts/cleanGeneratedFiles.js && node scripts/rewriteLinks.js && node scripts/removeDirectives.js && node scripts/removeSyncInfos.js && node scripts/removeTokenCount.js && node scripts/removeWhereSwap.js && node scripts/capitalizePageTitles.js", "test-k6": "k6 run tests/k6/generateyaml-test.js", "start-middleware": "node middlewareServer.js", "generate-custom-docs": "node scripts/generateCustomDocs.js", "generate-docs-with-middleware": "node scripts/generateDocsWithMiddleware.js", "generate-docs": "docusaurus graphql-to-doc"}, "dependencies": {"@docusaurus/core": "^3.8.1", "@docusaurus/preset-classic": "^3.8.1", "@graphiql/toolkit": "^0.11.1", "@graphql-markdown/docusaurus": "^1.29.0", "@graphql-tools/graphql-file-loader": "^8.0.19", "@graphql-tools/url-loader": "^8.0.31", "@mdx-js/react": "^3.0.0", "@vercel/analytics": "^1.5.0", "clsx": "^2.0.0", "dotenv": "^16.4.7", "express": "^5.1.0", "fast-deep-equal": "^3.1.3", "glob": "^11.0.1", "graphiql": "^3.8.3", "graphql": "^16.10.0", "graphql-ws": "^6.0.4", "js-yaml": "^4.1.0", "node-fetch": "^2.7.0", "prism-react-renderer": "^2.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "yaml": "^2.8.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.8.1", "@docusaurus/types": "3.7.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}