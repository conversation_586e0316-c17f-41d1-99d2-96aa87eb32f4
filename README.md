# OneSource Web3 API Developer Documentation

This is the codebase for the public-facing documentation of the OneSource Web3 API, built with [Docusaurus](https://docusaurus.io/).

The documentation is publicly available at:  
🔗 **[docs.onesource.io](https://docs.onesource.io)**

Although this repository is private, the generated site is publicly accessible and designed for **external developers** using the OneSource Web3 API.

### Installation
Make sure you have **Node.js (v18 or higher)** and **npm** installed.

```bash
npm install
```

### Local Development
Start the development server:

```bash
npm run start
```

The site will be available at http://localhost:3000/.

All content changes support hot reloading.

### Managing Environment Variables (for Local Development)

This project uses environment variables defined in Vercel to fetch the latest GraphQL schema and generate developer documentation. To work with the docs locally, you will need these variables in a local `.env` file.

There are two ways to get them:

#### Option 1: Pull variables from Vercel (recommended)

Use the Vercel CLI to pull environment variables into a local `.env` file in your project root:

1. Install the [Vercel CLI](https://vercel.com/docs/cli):
```bash
npm install -g vercel
```

2. Log into your Vercel account:
```bash
vercel login
```

3. Ensure you are inside your project directory, then link your local project to the correct Vercel project (if not already linked):

```bash
vercel link
```

4. Next, pull the environment variables:
```bash
vercel env pull .env
```
This will create (or overwrite) a `.env` file.
If you omit the filename, Vercel will default to `.env.local`, which may cause errors, so make sure to specify `.env`.

#### Option 2: Create the file manually
If you don’t have access to Vercel or prefer to configure manually, copy the example file:

```bash
cp .env.example .env
```
Then manually fill in the required environment variables.

⚠️ Important: This project requires the `.env` file specifically.
Using `.env.local` will not work, as the `scripts` expect `.env` to be present.

### Generating Developer Documentation (without Middleware)

The developer documentation is generated from the current OneSource Web3 API GraphQL schema by running `npm run generate-docs`. This process involves:

- Fetching the latest schema from the API endpoint specified in the `docusaurus.config.js` file.
- Automatically creating `.mdx` documentation files in the `./docs/onesource-web3-api-reference/` directory.
- Running cleanup `scripts` to remove unnecessary content and fix internal links.

Then, the generated `.mdx` files are imported into higher-level `.mdx` pages (e.g. `./docs/onesource-web3-api-reference/`), where additional content like descriptions and examples is added manually.

Make sure to run `npm run generate-docs` before `npm run build` whenever the schema changes to keep the docs up to date.

### Docs Generation with Middleware (YAML)
*(This process does not modify GraphQL schema or backend.)*

Custom descriptions for `queries` and `types` are stored in `.yaml` files inside the `schema-overrides/` folder.

- On repeated runs, existing custom descriptions are preserved.

- When introspection requests are proxied through `middlewareServer.js`, these YAML files are loaded and merged into the schema response.

- Final docs (`npm run generate-docs`) are generated from this augmented schema, combining real API structure with your custom content.

How to generate docs that include custom content from the middleware:

1. Generate YAML Files (if needed)

    Run this command to create initial `.yaml` files from current GraphQL schema:

    ```bash
    node scripts/generateCustomDocs.js
    ```

    - It creates files under `schema-overrides/queries/` and `schema-overrides/types/`.

    - If a file already exists, it will **not overwrite** your custom content, it only fills in missing fields.

2. Find the YAML File You Want to Edit

    - For a `query` like `balance`, edit:

    ```bash
    schema-overrides/queries/balance.yaml
    ```

    - For a `type` like `Balance`, edit:

    ```bash
    schema-overrides/types/objects/Balance.yaml
    ```

3. Add or Update Descriptions

    - Add a description to the `query`:

    ```yaml
        Query:
            fields:
                balance:
                    description: |
                        YOUR_DESCRIPTION_HERE
                    args:
                        owner: YOUR_DESCRIPTION_HERE
                        contract: YOUR_DESCRIPTION_HERE
                        tokenID: YOUR_DESCRIPTION_HERE
                        contractType: YOUR_DESCRIPTION_HERE
    ```
    - Add a description to the `type`:

    ```yaml
    Balance:
        description: |
            YOUR_DESCRIPTION_HERE
        fields:
            owner:
                description: YOUR_DESCRIPTION_HERE
            contractType:
                description: YOUR_DESCRIPTION_HERE
            contract:
                description: YOUR_DESCRIPTION_HERE
            token:
                description: YOUR_DESCRIPTION_HERE
            value:
                description: YOUR_DESCRIPTION_HERE
    ```

4. Preview Changes and Generate Final Docs

    - In one terminal, start the middleware server:

    ```bash
    npm run start-middleware
    ```
    This runs `middlewareServer.js` on http://localhost:4000/graphql

    - In another terminal, run the docs generation:

    ```bash
    npm run generate-docs
    ```
    This will fetch the schema via the middleware and build final docs with all your custom content included.

**Note:** The documentation generation depends on the middleware to add extra data to the schema, so it won’t work properly without running the middleware first.
If the middleware server is not running, docs generation will fail or produce incomplete data.

### Build for Production
To create a static production build:

```bash
npm run generate-docs # Run if schema has changed
npm run build
```
The static output will be placed in the `build` directory.

### Preview Production Build (optional)

```bash
npm run generate-docs # Run if schema has changed
npm run build
npm run serve
```
- First, creates a production build of the site in the `build` folder (after regenerating docs if schema changed)
- Then starts a local server to preview the final site as it will appear in production
- Useful for checking links, layout or metadata before deploying

### Deployment
This site is deployed via **[Vercel](https://vercel.com/)**.

#### Automatic Deployment
All changes pushed to the `develop` branch are automatically deployed to the production site:
**[https://docs.onesource.io](https://docs.onesource.io)**

To preview changes before merging, open a pull request - Vercel will generate a preview deployment URL for testing.

### Helpful Resources
- [Docusaurus Documentation](https://docusaurus.io/docs)
- [Markdown Features](https://docusaurus.io/docs/markdown-features)
- [Docusaurus Deployment Guide](https://docusaurus.io/docs/deployment)