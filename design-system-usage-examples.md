# Design System Package Usage Examples

## Installation

```bash
# Install the private package
npm install @blockparty/design-system

# For Docusaurus projects
npm install @blockparty/design-system @docusaurus/core

# For Next.js/Chakra projects  
npm install @blockparty/design-system @chakra-ui/react @emotion/react
```

---

## 🏗️ Setup in Different Projects

### Docusaurus Project Setup

#### 1. Configure in `docusaurus.config.js`
```javascript
const { colors, typography } = require('@blockparty/design-system/tokens');

module.exports = {
  title: 'OneSource Docs',
  themeConfig: {
    colorMode: {
      defaultMode: 'light',
      disableSwitch: false,
    },
    navbar: {
      style: 'primary',
    },
  },
  stylesheets: [
    '@blockparty/design-system/dist/docusaurus.css'
  ],
};
```

#### 2. Apply theme in `src/css/custom.css`
```css
/* Import design system tokens */
@import '@blockparty/design-system/tokens/css-variables.css';

:root {
  /* Override Docusaurus variables with design system tokens */
  --ifm-color-primary: var(--bp-color-primary);
  --ifm-color-primary-dark: var(--bp-color-primary-dark);
  --ifm-font-family-base: var(--bp-font-body);
  --ifm-heading-font-family: var(--bp-font-heading);
}

[data-theme='dark'] {
  --ifm-background-color: var(--bp-color-primary-dark);
  --ifm-font-color-base: var(--bp-color-white);
}
```

#### 3. Use components in MDX files
```mdx
---
title: API Documentation
---

import { Button, Card } from '@blockparty/design-system';

# Welcome to OneSource API

<Card variant="feature" title="GraphQL API">
  Query blockchain data with our powerful GraphQL interface.
</Card>

<Button variant="primary" size="lg">
  Get Started
</Button>
```

### Next.js/Chakra Project Setup

#### 1. Configure theme in `src/theme/index.ts`
```typescript
import { extendTheme } from '@chakra-ui/react';
import { createChakraTheme } from '@blockparty/design-system/adapters/chakra';

// Create base theme from design system
const designSystemTheme = createChakraTheme();

// Extend with project-specific customizations
const theme = extendTheme({
  ...designSystemTheme,
  // Add any project-specific overrides here
  components: {
    ...designSystemTheme.components,
    // Custom component styles
  }
});

export default theme;
```

#### 2. Setup providers in `app/layout.tsx`
```typescript
'use client';
import { ChakraProvider } from '@chakra-ui/react';
import theme from '@/theme';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ChakraProvider theme={theme}>
          {children}
        </ChakraProvider>
      </body>
    </html>
  );
}
```

#### 3. Use components in pages
```typescript
import { Button, Card, Navigation } from '@blockparty/design-system';
import { Box, Container } from '@chakra-ui/react';

export default function HomePage() {
  const navItems = [
    { label: 'Home', href: '/' },
    { label: 'About', href: '/about' },
    { label: 'Contact', href: '/contact' }
  ];

  return (
    <Box>
      <Navigation items={navItems} theme="dark" />
      
      <Container maxW="container.xl" py={8}>
        <Card 
          variant="feature"
          title="Welcome to OneSource"
          description="The most powerful blockchain data platform"
        />
        
        <Button variant="primary" size="lg" mt={4}>
          Explore API
        </Button>
      </Container>
    </Box>
  );
}
```

---

## 🎨 Using Design Tokens

### Direct Token Usage
```typescript
import { colors, typography, spacing } from '@blockparty/design-system/tokens';

// In styled components
const StyledButton = styled.button`
  background-color: ${colors.primary};
  color: ${colors.primaryDark};
  font-family: ${typography.fonts.body};
  padding: ${spacing[4]} ${spacing[6]};
  border-radius: ${spacing[2]};
`;

// In CSS-in-JS
const buttonStyles = {
  backgroundColor: colors.primary,
  color: colors.white,
  fontSize: typography.fontSizes.md,
  padding: `${spacing[3]} ${spacing[5]}`
};
```

### CSS Variables (Docusaurus)
```css
.custom-component {
  background-color: var(--bp-color-primary);
  color: var(--bp-color-primary-dark);
  font-family: var(--bp-font-body);
  padding: var(--bp-space-4) var(--bp-space-6);
}
```

### Chakra Theme Tokens
```typescript
import { Box, Text } from '@chakra-ui/react';

function CustomComponent() {
  return (
    <Box bg="primary" color="primaryDark" p={4}>
      <Text fontFamily="body" fontSize="md">
        Using design system tokens through Chakra
      </Text>
    </Box>
  );
}
```

---

## 🧩 Component Examples

### Button Component
```typescript
import { Button } from '@blockparty/design-system';

function ButtonExamples() {
  return (
    <div>
      {/* Primary button */}
      <Button variant="primary" size="lg">
        Get Started
      </Button>
      
      {/* Secondary button */}
      <Button variant="secondary" size="md">
        Learn More
      </Button>
      
      {/* Icon button */}
      <Button variant="icon" size="sm">
        <SearchIcon />
      </Button>
      
      {/* Loading state */}
      <Button variant="primary" loading>
        Submitting...
      </Button>
      
      {/* Disabled state */}
      <Button variant="primary" disabled>
        Disabled
      </Button>
    </div>
  );
}
```

### Form Components
```typescript
import { Input, FormField, Checkbox, Button } from '@blockparty/design-system';

function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
    newsletter: false
  });

  return (
    <form>
      <FormField label="Name" required>
        <Input
          variant="default"
          placeholder="Your name"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
        />
      </FormField>
      
      <FormField label="Email" required>
        <Input
          variant="email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
        />
      </FormField>
      
      <FormField label="Message">
        <Input
          variant="default"
          placeholder="Your message"
          multiline
          rows={4}
          value={formData.message}
          onChange={(e) => setFormData({...formData, message: e.target.value})}
        />
      </FormField>
      
      <Checkbox
        label="Subscribe to newsletter"
        checked={formData.newsletter}
        onChange={(checked) => setFormData({...formData, newsletter: checked})}
      />
      
      <Button variant="primary" type="submit">
        Send Message
      </Button>
    </form>
  );
}
```

### Navigation Component
```typescript
import { Navigation } from '@blockparty/design-system';

function AppNavigation() {
  const navigationItems = [
    {
      label: 'Documentation',
      href: '/docs',
      children: [
        { label: 'Getting Started', href: '/docs/getting-started' },
        { label: 'API Reference', href: '/docs/api' },
        { label: 'Examples', href: '/docs/examples' }
      ]
    },
    {
      label: 'Blog',
      href: '/blog'
    },
    {
      label: 'GitHub',
      href: 'https://github.com/blockparty',
      external: true
    }
  ];

  return (
    <Navigation
      items={navigationItems}
      theme="dark"
      logo={<img src="/logo.svg" alt="OneSource" />}
    />
  );
}
```

### Card Layouts
```typescript
import { Card } from '@blockparty/design-system';

function FeatureCards() {
  const features = [
    {
      title: 'Real-time Data',
      description: 'Get the latest blockchain data instantly',
      image: '/icons/realtime.svg'
    },
    {
      title: 'GraphQL API',
      description: 'Powerful and flexible query interface',
      image: '/icons/graphql.svg'
    },
    {
      title: 'Multi-chain',
      description: 'Support for multiple blockchain networks',
      image: '/icons/multichain.svg'
    }
  ];

  return (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '24px' }}>
      {features.map((feature, index) => (
        <Card
          key={index}
          variant="feature"
          title={feature.title}
          description={feature.description}
          image={feature.image}
        />
      ))}
    </div>
  );
}
```

---

## 🎯 Advanced Usage

### Custom Theme Extension
```typescript
// Extending the design system theme
import { colors, typography } from '@blockparty/design-system/tokens';

const customTheme = {
  colors: {
    ...colors,
    // Add project-specific colors
    brand: {
      primary: colors.primary,
      secondary: '#FF6B6B',
      tertiary: '#4ECDC4'
    }
  },
  typography: {
    ...typography,
    // Add custom font sizes
    fontSizes: {
      ...typography.fontSizes,
      '7xl': '4.5rem',
      '8xl': '6rem'
    }
  }
};
```

### Responsive Design
```typescript
import { breakpoints } from '@blockparty/design-system/tokens';

const responsiveStyles = {
  fontSize: {
    base: '16px',
    [breakpoints.md]: '18px',
    [breakpoints.lg]: '20px'
  },
  padding: {
    base: '16px',
    [breakpoints.md]: '24px',
    [breakpoints.lg]: '32px'
  }
};
```

### Dark Mode Support
```typescript
import { Button } from '@blockparty/design-system';

function ThemedButton() {
  return (
    <Button 
      variant="primary" 
      theme="dark"  // Automatically adapts colors for dark theme
    >
      Dark Mode Button
    </Button>
  );
}
```

---

## 📱 Platform-Specific Features

### Docusaurus-specific
```mdx
import { SearchBox, Tag } from '@blockparty/design-system';

# API Documentation

<SearchBox 
  placeholder="Search documentation..." 
  onSearch={(query) => console.log(query)}
/>

Tags: <Tag label="GraphQL" variant="category" /> <Tag label="REST" variant="category" />
```

### Next.js-specific
```typescript
import { ContactForm } from '@blockparty/design-system';

function ContactPage() {
  const handleSubmit = async (data) => {
    // Handle form submission
    await fetch('/api/contact', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  };

  return (
    <ContactForm 
      onSubmit={handleSubmit}
      theme="light"
    />
  );
}
```

This package provides a consistent design language across all Blockparty projects while maintaining the flexibility needed for each platform's specific requirements.
