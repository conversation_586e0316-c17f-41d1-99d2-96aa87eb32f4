// @ts-check
require('dotenv').config();
const { UrlLoader } = require('@graphql-tools/url-loader');
const {themes} = require('prism-react-renderer');

/** @type {import('@docusaurus/types').Config} */
const config = {
  title: 'OneSource Web3 API',
  tagline: 'Powering Web3 with Scalable Backend Infrastructure',
  url: 'https://your-docs-site.com', // Replace with your site URL
  baseUrl: '/',
  favicon: 'img/favicon.ico',

  organizationName: 'your-org', // GitHub org/user name
  projectName: 'onesource-docs', // Repo name

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  i18n: {
    defaultLocale: 'en',
    locales: ['en'],
  },

  presets: [
    [
      'classic',
      /** @type {import('@docusaurus/preset-classic').Options} */
      ({
        docs: {
          sidebarPath: require.resolve('./sidebars.js'),
          routeBasePath: '/', // Serve docs at root
          editUrl: 'https://github.com/your-org/onesource-docs/edit/main/',
        },
        blog: false,
        theme: {
          customCss: require.resolve('./src/css/custom.css'),
        },
      }),
    ],
  ],

  themeConfig:
    /** @type {import('@docusaurus/preset-classic').ThemeConfig} */
    ({
      colorMode: {
        defaultMode: 'dark',
        disableSwitch: false, // Disable the color mode switch
        respectPrefersColorScheme: true, // Do not respect user's system preference
      },
      navbar: {
        title: '',
        style: 'dark',
        logo: {
          alt: 'OneSource Web3 API',
          src: 'img/logo-light.svg',
          srcDark: 'img/logo-dark.svg',
          width: 150,
          height: 20,
        },
        items: [
          {
            type: 'doc',
            docId: 'intro',
            position: 'left',
            label: 'Overview',
          },
          {
            type: 'doc',
            docId: 'getting-started/README',
            label: 'Getting Started',
            position: 'left',
          },
          {
            type: 'doc',
            docId: 'guides/README',
            label: 'Guides',
            position: 'left',
          },
          {
            type: 'doc',
            docId: 'onesource-web3-api-reference/README',
            label: 'API Reference',
            position: 'left',
          },
          {
            type: 'doc',
            docId: 'account-access',
            label: 'Account Access',
            position: 'right',
          },
        ],
      },
      footer: {
        style: 'dark',
        links: [
          {
            title: 'Docs',
            items: [
              {
                label: 'Overview',
                to: '/',
              },
              {
                label: 'Getting Started',
                to: '/getting-started/',
              },
              {
                label: 'Guides',
                to: '/guides/',
              },
              {
                label: 'API Reference',
                to: '/onesource-web3-api-reference/',
              },
            ],
          },
          {
            title: 'Community',
            items: [
              {
                label: 'LinkedIn',
                href: 'https://www.linkedin.com/company/1onesource',
              },
              {
                label: 'Twitter',
                href: 'https://x.com/_onesource',
              },
            ],
          },
          {
            title: 'More',
            items: [
              {
                label: 'Blog',
                href: 'https://www.onesource.io/blog',
              },
            ],
          },
        ],
        copyright: `Copyright © ${new Date().getFullYear()} Blockparty, Inc.`,
      },
      prism: {
        theme: themes.github,
        darkTheme: themes.dracula,
        additionalLanguages: ['graphql'], // For GraphQL syntax highlighting
      },
      // For API playground integration
      algolia: {
        // Optional: Algolia search config
        appId: '69F6BLY1VX',
        apiKey: '********************************',
        indexName: 'onesource',
      },
    }),
};

// Add GraphQL playground plugin if needed
if (process.env.ENABLE_GRAPHQL_PLAYGROUND === 'true') {
  config.plugins = [
    [
      'docusaurus-graphql-playground',
      {
        endpoint: 'https://your-api-endpoint/graphql',
      },
    ],
  ];
}

module.exports = {
  ...config,
  customFields: {
    GRAPHQL_ENDPOINT: 'https://api.onesource.io/v1/ethereum/graphql',
    ONESOURCE_API_TOKEN: process.env.ONESOURCE_API_TOKEN,
  },
  headTags: [
    {
      tagName: 'link',
      attributes: {
        rel: 'preload',
        href: '/fonts/Figtree.woff2',
        as: 'font',
        type: 'font/woff2',
        crossorigin: 'anonymous',
        fetchpriority: 'high'
      }
    },
    {
      tagName: 'link',
      attributes: {
        rel: 'preload',
        href: '/fonts/Figtree-Bold.woff2',
        as: 'font',
        type: 'font/woff2',
        crossorigin: 'anonymous',
        fetchpriority: 'high'
      }
    },
    {
      tagName: 'link',
      attributes: {
        rel: 'preload',
        href: '/fonts/mabry.woff2',
        as: 'font',
        type: 'font/woff2',
        crossorigin: 'anonymous',
        fetchpriority: 'high'
      }
    },
    {
      tagName: 'link',
      attributes: {
        rel: 'preload',
        href: '/fonts/Neon.woff2',
        as: 'font',
        type: 'font/woff2',
        crossorigin: 'anonymous',
        fetchpriority: 'high'
      }
    },
    {
      tagName: 'link',
      attributes: {
        rel: 'preload',
        href: '/fonts/Neon-Regular.woff2',
        as: 'font',
        type: 'font/woff2',
        crossorigin: 'anonymous',
        fetchpriority: 'high'
      }
    }
  ],
  plugins: [
    [
      "@graphql-markdown/docusaurus",
      /** @type {import('@graphql-markdown/types').ConfigOptions} */
      {
        /* URL of the local middleware GraphQL server, instead of "https://api.onesource.io/v1/ethereum/graphql"
          This middleware adds extra content from extraDocs.yaml into the schema responses.
        */ 
        schema: 'http://localhost:4000/graphql', // use middleware server
        rootPath: "./docs", // where generated .mdx files will be stored
        baseURL: "onesource-web3-api-reference", // URL path in the browser - must match sidebar and real URL prefix
        homepage: "./docs/onesource-web3-api-reference/schema.md", // entry point page of the generated docs
        loaders: {
          UrlLoader: {
            module: "@graphql-tools/url-loader",
            options: {
              headers: {
                "x-bp-token": process.env.ONESOURCE_API_TOKEN || ""
              }
            }
          }
        },
        // Optional advanced settings
        pretty: true,
        customDirective: true,
        docOptions: {
          index: true, // generates index.md for types/queries list
        },
      }
    ]
  ]
};